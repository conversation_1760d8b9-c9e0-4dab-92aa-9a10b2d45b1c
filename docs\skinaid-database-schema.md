# Tài Liệu Schema Cơ Sở Dữ Liệu SkinAid

## Tổng Quan Hệ Thống

Cơ sở dữ liệu **SkinAid_db** đ<PERSON><PERSON><PERSON> thiết kế để hỗ trợ hệ thống phát hiện và quản lý vết thương sử dụng AI. <PERSON>ệ thống áp dụng kiến trúc modular monolith với các module rõ ràng: Authentication (Xác thực), Wound Management (Quản lý vết thương), Diagnosis (Chẩn đoán), và Treatment (Điều trị).

### Nguyên Tắc Thiết Kế
- **Bảo mật**: <PERSON><PERSON> thủ các tiêu chuẩn bảo mật y tế
- **Hiệu suất**: Tối ưu hóa cho PostgreSQL với indexes và constraints phù hợp
- **Mở rộng**: Thi<PERSON><PERSON> kế linh hoạt để mở rộng tính năng
- **<PERSON><PERSON> thủ**: Đ<PERSON><PERSON> ứng yêu cầu lưu trữ dữ liệu y tế

## Module Authentication (Xác Thực)

### 1. Bảng `users` - Thông Tin Người Dùng

**Mục đích**: Lưu trữ thông tin cơ bản và thông tin xác thực của người dùng

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Định danh duy nhất của người dùng |
| `username` | VARCHAR(50) | UNIQUE, NOT NULL | Tên đăng nhập duy nhất |
| `email` | VARCHAR(255) | UNIQUE, NOT NULL | Địa chỉ email (dùng để đăng nhập) |
| `password_hash` | TEXT | NOT NULL | Mật khẩu đã được mã hóa bcrypt |
| `first_name` | VARCHAR(100) | NULL | Tên |
| `last_name` | VARCHAR(100) | NULL | Họ |
| `phone_number` | VARCHAR(20) | NULL | Số điện thoại |
| `is_active` | BOOLEAN | DEFAULT true, NOT NULL | Tài khoản có hoạt động không |
| `is_verified` | BOOLEAN | DEFAULT false, NOT NULL | Email đã được xác thực chưa |
| `is_superuser` | BOOLEAN | DEFAULT false, NOT NULL | Có quyền quản trị viên không |
| `account_status` | VARCHAR(20) | DEFAULT 'pending', NOT NULL | Trạng thái tài khoản |
| `last_login` | TIMESTAMPTZ | NULL | Lần đăng nhập cuối cùng |
| `failed_login_attempts` | INTEGER | DEFAULT 0, NOT NULL | Số lần đăng nhập thất bại liên tiếp |
| `locked_until` | TIMESTAMPTZ | NULL | Tài khoản bị khóa đến khi nào |
| `metadata` | JSONB | DEFAULT '{}' | Thông tin bổ sung (dữ liệu y tế) |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo tài khoản |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật cuối |

**Indexes**:
- `idx_users_email` - Tìm kiếm theo email
- `idx_users_username` - Tìm kiếm theo username
- `idx_users_active_verified` - Lọc người dùng hoạt động và đã xác thực
- `idx_users_metadata` (GIN) - Tìm kiếm trong metadata JSON

**Constraints**:
- `chk_users_email_format` - Kiểm tra định dạng email hợp lệ
- `chk_users_username_format` - Username chỉ chứa chữ, số và dấu gạch dưới
- `chk_users_account_status` - Trạng thái phải là: pending, active, suspended, deactivated

**Dữ liệu mẫu**:
```sql
INSERT INTO users (username, email, password_hash, first_name, last_name, account_status) VALUES
('bac_si_nguyen', '<EMAIL>', '$2b$12$...', 'Nguyễn', 'Văn A', 'active'),
('benh_nhan_tran', '<EMAIL>', '$2b$12$...', 'Trần', 'Thị B', 'active');
```

### 2. Bảng `roles` - Vai Trò Hệ Thống

**Mục đích**: Định nghĩa các vai trò khác nhau trong hệ thống y tế

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Định danh vai trò |
| `name` | VARCHAR(50) | UNIQUE, NOT NULL | Tên vai trò (patient, healthcare_provider, admin) |
| `display_name` | VARCHAR(100) | NOT NULL | Tên hiển thị |
| `description` | TEXT | NULL | Mô tả chi tiết vai trò |
| `level` | INTEGER | DEFAULT 0, NOT NULL | Cấp độ phân quyền (số càng cao quyền càng lớn) |
| `permissions` | JSONB | DEFAULT '[]' | Danh sách quyền hạn |
| `is_active` | BOOLEAN | DEFAULT true, NOT NULL | Vai trò có hoạt động không |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật |

**Vai trò mặc định**:
```sql
INSERT INTO roles (name, display_name, description, level, permissions) VALUES
('patient', 'Bệnh Nhân', 'Người dùng cơ bản có thể quản lý vết thương của mình', 1, 
 '["view_own_wounds", "create_wound_report", "view_own_profile"]'),
('healthcare_provider', 'Nhân Viên Y Tế', 'Chuyên gia y tế có thể quản lý dữ liệu bệnh nhân', 5, 
 '["view_patient_wounds", "create_diagnosis", "update_wound_status", "create_treatment_plan"]'),
('admin', 'Quản Trị Viên', 'Quản trị viên hệ thống với quyền truy cập đầy đủ', 10, 
 '["manage_users", "manage_roles", "view_all_data", "system_configuration"]');
```

### 3. Bảng `user_roles` - Phân Quyền Người Dùng

**Mục đích**: Bảng liên kết many-to-many giữa người dùng và vai trò

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `user_id` | UUID | PRIMARY KEY, FOREIGN KEY → users(id) | ID người dùng |
| `role_id` | UUID | PRIMARY KEY, FOREIGN KEY → roles(id) | ID vai trò |
| `assigned_by` | UUID | FOREIGN KEY → users(id) | Người phân quyền |
| `assigned_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian phân quyền |
| `expires_at` | TIMESTAMPTZ | NULL | Thời gian hết hạn (tùy chọn) |
| `is_active` | BOOLEAN | DEFAULT true, NOT NULL | Phân quyền có hoạt động không |
| `metadata` | JSONB | DEFAULT '{}' | Thông tin bổ sung |

### 4. Bảng `sessions` - Quản Lý Phiên Đăng Nhập

**Mục đích**: Quản lý phiên đăng nhập và JWT tokens

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID phiên |
| `user_id` | UUID | FOREIGN KEY → users(id), NOT NULL | ID người dùng |
| `session_token` | TEXT | UNIQUE, NOT NULL | JWT token hoặc session ID |
| `refresh_token` | TEXT | UNIQUE | Token để làm mới |
| `device_info` | JSONB | DEFAULT '{}' | Thông tin thiết bị |
| `ip_address` | INET | NULL | Địa chỉ IP client |
| `user_agent` | TEXT | NULL | Thông tin trình duyệt |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `expires_at` | TIMESTAMPTZ | NOT NULL | Thời gian hết hạn |
| `last_accessed` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Lần truy cập cuối |
| `is_active` | BOOLEAN | DEFAULT true, NOT NULL | Phiên có hoạt động không |
| `revoked_at` | TIMESTAMPTZ | NULL | Thời gian thu hồi |
| `revoked_by` | UUID | FOREIGN KEY → users(id) | Người thu hồi phiên |
| `revoke_reason` | VARCHAR(100) | NULL | Lý do thu hồi |

### 5. Bảng `password_reset_tokens` - Token Đặt Lại Mật Khẩu

**Mục đích**: Quản lý token để đặt lại mật khẩu một cách bảo mật

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID token |
| `user_id` | UUID | FOREIGN KEY → users(id), NOT NULL | ID người dùng |
| `token` | TEXT | UNIQUE, NOT NULL | Token bảo mật (gửi cho người dùng) |
| `token_hash` | TEXT | NOT NULL | Hash của token (lưu trong DB) |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `expires_at` | TIMESTAMPTZ | NOT NULL | Thời gian hết hạn (thường 1-24 giờ) |
| `used_at` | TIMESTAMPTZ | NULL | Thời gian sử dụng token |
| `ip_address` | INET | NULL | IP yêu cầu đặt lại |
| `user_agent` | TEXT | NULL | Trình duyệt yêu cầu |
| `is_used` | BOOLEAN | DEFAULT false, NOT NULL | Token đã được sử dụng chưa |
| `is_revoked` | BOOLEAN | DEFAULT false, NOT NULL | Token đã bị thu hồi chưa |

### 6. Bảng `email_verification_tokens` - Token Xác Thực Email

**Mục đích**: Quản lý quy trình xác thực email

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID token |
| `user_id` | UUID | FOREIGN KEY → users(id), NOT NULL | ID người dùng |
| `email` | VARCHAR(255) | NOT NULL | Email cần xác thực |
| `token` | TEXT | UNIQUE, NOT NULL | Token xác thực |
| `token_hash` | TEXT | NOT NULL | Hash của token |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `expires_at` | TIMESTAMPTZ | NOT NULL | Thời gian hết hạn (24-72 giờ) |
| `verified_at` | TIMESTAMPTZ | NULL | Thời gian xác thực thành công |
| `token_type` | VARCHAR(20) | DEFAULT 'registration', NOT NULL | Loại xác thực |
| `ip_address` | INET | NULL | IP yêu cầu xác thực |
| `user_agent` | TEXT | NULL | Trình duyệt yêu cầu |
| `is_used` | BOOLEAN | DEFAULT false, NOT NULL | Token đã sử dụng chưa |
| `is_revoked` | BOOLEAN | DEFAULT false, NOT NULL | Token đã thu hồi chưa |

**Loại token**: registration, email_change, reactivation

## Module Wound Management (Quản Lý Vết Thương)

### 7. Bảng `wounds` - Thông Tin Vết Thương

**Mục đích**: Lưu trữ thông tin chi tiết về vết thương của bệnh nhân

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID vết thương |
| `patient_id` | UUID | FOREIGN KEY → users(id), NOT NULL | ID bệnh nhân |
| `wound_code` | VARCHAR(20) | UNIQUE, NOT NULL | Mã vết thương (tự động tạo) |
| `title` | VARCHAR(200) | NOT NULL | Tiêu đề mô tả vết thương |
| `description` | TEXT | NULL | Mô tả chi tiết |
| `location_body_part` | VARCHAR(100) | NOT NULL | Vị trí trên cơ thể |
| `location_description` | TEXT | NULL | Mô tả vị trí chi tiết |
| `wound_type` | VARCHAR(50) | NULL | Loại vết thương (do AI phân loại) |
| `severity_level` | INTEGER | CHECK (severity_level BETWEEN 1 AND 5) | Mức độ nghiêm trọng (1-5) |
| `size_length_mm` | DECIMAL(8,2) | NULL | Chiều dài (mm) |
| `size_width_mm` | DECIMAL(8,2) | NULL | Chiều rộng (mm) |
| `size_depth_mm` | DECIMAL(8,2) | NULL | Chiều sâu (mm) |
| `color_description` | TEXT | NULL | Mô tả màu sắc |
| `pain_level` | INTEGER | CHECK (pain_level BETWEEN 0 AND 10) | Mức độ đau (0-10) |
| `bleeding_status` | VARCHAR(20) | DEFAULT 'none' | Tình trạng chảy máu |
| `infection_signs` | JSONB | DEFAULT '[]' | Dấu hiệu nhiễm trùng |
| `healing_stage` | VARCHAR(30) | DEFAULT 'fresh' | Giai đoạn lành |
| `status` | VARCHAR(20) | DEFAULT 'active' | Trạng thái vết thương |
| `is_chronic` | BOOLEAN | DEFAULT false | Vết thương mãn tính |
| `occurred_at` | TIMESTAMPTZ | NOT NULL | Thời gian xảy ra |
| `reported_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP | Thời gian báo cáo |
| `metadata` | JSONB | DEFAULT '{}' | Thông tin bổ sung |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật |

**Indexes**:
- `idx_wounds_patient_id` - Tìm vết thương theo bệnh nhân
- `idx_wounds_wound_code` - Tìm theo mã vết thương
- `idx_wounds_wound_type` - Lọc theo loại vết thương
- `idx_wounds_status` - Lọc theo trạng thái
- `idx_wounds_occurred_at` - Sắp xếp theo thời gian xảy ra

### 8. Bảng `wound_images` - Hình Ảnh Vết Thương

**Mục đích**: Lưu trữ hình ảnh vết thương để AI phân tích

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID hình ảnh |
| `wound_id` | UUID | FOREIGN KEY → wounds(id), NOT NULL | ID vết thương |
| `uploaded_by` | UUID | FOREIGN KEY → users(id), NOT NULL | Người tải lên |
| `file_name` | VARCHAR(255) | NOT NULL | Tên file gốc |
| `file_path` | TEXT | NOT NULL | Đường dẫn lưu trữ |
| `file_size` | BIGINT | NOT NULL | Kích thước file (bytes) |
| `mime_type` | VARCHAR(100) | NOT NULL | Loại file (image/jpeg, image/png) |
| `image_width` | INTEGER | NULL | Chiều rộng ảnh (pixels) |
| `image_height` | INTEGER | NULL | Chiều cao ảnh (pixels) |
| `capture_date` | TIMESTAMPTZ | NULL | Thời gian chụp ảnh |
| `camera_info` | JSONB | DEFAULT '{}' | Thông tin camera/thiết bị |
| `ai_processed` | BOOLEAN | DEFAULT false | Đã xử lý bằng AI chưa |
| `ai_analysis_result` | JSONB | DEFAULT '{}' | Kết quả phân tích AI |
| `is_primary` | BOOLEAN | DEFAULT false | Ảnh chính của vết thương |
| `notes` | TEXT | NULL | Ghi chú về ảnh |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tải lên |

### 9. Bảng `wound_assessments` - Đánh Giá Vết Thương

**Mục đích**: Lưu trữ các lần đánh giá vết thương theo thời gian

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID đánh giá |
| `wound_id` | UUID | FOREIGN KEY → wounds(id), NOT NULL | ID vết thương |
| `assessed_by` | UUID | FOREIGN KEY → users(id), NOT NULL | Người đánh giá |
| `assessment_type` | VARCHAR(30) | NOT NULL | Loại đánh giá (initial, follow_up, ai_analysis) |
| `healing_progress` | VARCHAR(30) | NULL | Tiến trình lành (improving, stable, worsening) |
| `size_change` | VARCHAR(20) | NULL | Thay đổi kích thước |
| `color_change` | TEXT | NULL | Thay đổi màu sắc |
| `pain_level` | INTEGER | CHECK (pain_level BETWEEN 0 AND 10) | Mức độ đau hiện tại |
| `infection_risk` | VARCHAR(20) | DEFAULT 'low' | Nguy cơ nhiễm trùng |
| `recommendations` | TEXT | NULL | Khuyến nghị điều trị |
| `next_assessment_date` | DATE | NULL | Ngày đánh giá tiếp theo |
| `assessment_data` | JSONB | DEFAULT '{}' | Dữ liệu đánh giá chi tiết |
| `notes` | TEXT | NULL | Ghi chú bổ sung |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian đánh giá |

## Module Diagnosis (Chẩn Đoán)

### 10. Bảng `diagnoses` - Chẩn Đoán Y Khoa

**Mục đích**: Lưu trữ chẩn đoán y khoa cho vết thương

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID chẩn đoán |
| `wound_id` | UUID | FOREIGN KEY → wounds(id), NOT NULL | ID vết thương |
| `diagnosed_by` | UUID | FOREIGN KEY → users(id), NOT NULL | Bác sĩ chẩn đoán |
| `diagnosis_code` | VARCHAR(20) | NULL | Mã chẩn đoán (ICD-10) |
| `diagnosis_name` | VARCHAR(200) | NOT NULL | Tên chẩn đoán |
| `description` | TEXT | NULL | Mô tả chi tiết |
| `confidence_level` | DECIMAL(3,2) | CHECK (confidence_level BETWEEN 0 AND 1) | Độ tin cậy (0-1) |
| `diagnosis_source` | VARCHAR(20) | NOT NULL | Nguồn chẩn đoán (ai, doctor, combined) |
| `ai_model_version` | VARCHAR(50) | NULL | Phiên bản mô hình AI |
| `supporting_evidence` | JSONB | DEFAULT '[]' | Bằng chứng hỗ trợ |
| `differential_diagnoses` | JSONB | DEFAULT '[]' | Chẩn đoán phân biệt |
| `severity_assessment` | VARCHAR(20) | NULL | Đánh giá mức độ nghiêm trọng |
| `urgency_level` | VARCHAR(20) | DEFAULT 'routine' | Mức độ cấp thiết |
| `is_confirmed` | BOOLEAN | DEFAULT false | Chẩn đoán đã được xác nhận |
| `confirmed_by` | UUID | FOREIGN KEY → users(id) | Người xác nhận |
| `confirmed_at` | TIMESTAMPTZ | NULL | Thời gian xác nhận |
| `notes` | TEXT | NULL | Ghi chú bổ sung |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian chẩn đoán |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật |

## Mối Quan Hệ Giữa Các Bảng

```
users (1) ←→ (M) user_roles (M) ←→ (1) roles
users (1) ←→ (M) sessions
users (1) ←→ (M) password_reset_tokens
users (1) ←→ (M) email_verification_tokens
users (1) ←→ (M) wounds [as patient]
users (1) ←→ (M) wound_images [as uploader]
users (1) ←→ (M) wound_assessments [as assessor]
users (1) ←→ (M) diagnoses [as doctor]

wounds (1) ←→ (M) wound_images
wounds (1) ←→ (M) wound_assessments
wounds (1) ←→ (M) diagnoses
```

## Module Treatment (Điều Trị)

### 11. Bảng `treatment_plans` - Kế Hoạch Điều Trị

**Mục đích**: Lưu trữ kế hoạch điều trị cho vết thương

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID kế hoạch điều trị |
| `wound_id` | UUID | FOREIGN KEY → wounds(id), NOT NULL | ID vết thương |
| `diagnosis_id` | UUID | FOREIGN KEY → diagnoses(id) | ID chẩn đoán liên quan |
| `created_by` | UUID | FOREIGN KEY → users(id), NOT NULL | Bác sĩ tạo kế hoạch |
| `plan_name` | VARCHAR(200) | NOT NULL | Tên kế hoạch điều trị |
| `description` | TEXT | NULL | Mô tả chi tiết kế hoạch |
| `treatment_goals` | JSONB | DEFAULT '[]' | Mục tiêu điều trị |
| `estimated_duration_days` | INTEGER | NULL | Thời gian điều trị dự kiến (ngày) |
| `priority_level` | VARCHAR(20) | DEFAULT 'medium' | Mức độ ưu tiên |
| `status` | VARCHAR(20) | DEFAULT 'active' | Trạng thái kế hoạch |
| `start_date` | DATE | NOT NULL | Ngày bắt đầu |
| `end_date` | DATE | NULL | Ngày kết thúc |
| `review_frequency_days` | INTEGER | DEFAULT 7 | Tần suất đánh giá (ngày) |
| `next_review_date` | DATE | NULL | Ngày đánh giá tiếp theo |
| `special_instructions` | TEXT | NULL | Hướng dẫn đặc biệt |
| `contraindications` | JSONB | DEFAULT '[]' | Chống chỉ định |
| `expected_outcomes` | TEXT | NULL | Kết quả mong đợi |
| `notes` | TEXT | NULL | Ghi chú bổ sung |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật |

### 12. Bảng `treatments` - Các Liệu Pháp Điều Trị

**Mục đích**: Lưu trữ thông tin về các liệu pháp điều trị cụ thể

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID liệu pháp |
| `treatment_plan_id` | UUID | FOREIGN KEY → treatment_plans(id), NOT NULL | ID kế hoạch điều trị |
| `treatment_type` | VARCHAR(50) | NOT NULL | Loại điều trị |
| `treatment_name` | VARCHAR(200) | NOT NULL | Tên liệu pháp |
| `description` | TEXT | NULL | Mô tả chi tiết |
| `dosage_instructions` | TEXT | NULL | Hướng dẫn liều lượng |
| `frequency` | VARCHAR(50) | NULL | Tần suất thực hiện |
| `duration_days` | INTEGER | NULL | Thời gian điều trị (ngày) |
| `administration_route` | VARCHAR(30) | NULL | Đường dùng thuốc |
| `side_effects` | JSONB | DEFAULT '[]' | Tác dụng phụ |
| `monitoring_requirements` | TEXT | NULL | Yêu cầu theo dõi |
| `cost_estimate` | DECIMAL(10,2) | NULL | Chi phí ước tính |
| `is_prescription_required` | BOOLEAN | DEFAULT false | Cần đơn thuốc không |
| `priority_order` | INTEGER | DEFAULT 1 | Thứ tự ưu tiên |
| `status` | VARCHAR(20) | DEFAULT 'prescribed' | Trạng thái điều trị |
| `start_date` | DATE | NOT NULL | Ngày bắt đầu |
| `end_date` | DATE | NULL | Ngày kết thúc |
| `notes` | TEXT | NULL | Ghi chú |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật |

### 13. Bảng `treatment_records` - Hồ Sơ Thực Hiện Điều Trị

**Mục đích**: Ghi lại quá trình thực hiện điều trị

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID hồ sơ |
| `treatment_id` | UUID | FOREIGN KEY → treatments(id), NOT NULL | ID liệu pháp |
| `administered_by` | UUID | FOREIGN KEY → users(id) | Người thực hiện |
| `administered_at` | TIMESTAMPTZ | NOT NULL | Thời gian thực hiện |
| `dosage_given` | VARCHAR(100) | NULL | Liều lượng đã cho |
| `administration_method` | VARCHAR(50) | NULL | Phương pháp thực hiện |
| `patient_response` | TEXT | NULL | Phản ứng của bệnh nhân |
| `effectiveness_rating` | INTEGER | CHECK (effectiveness_rating BETWEEN 1 AND 5) | Đánh giá hiệu quả (1-5) |
| `side_effects_observed` | JSONB | DEFAULT '[]' | Tác dụng phụ quan sát |
| `complications` | TEXT | NULL | Biến chứng |
| `next_scheduled` | TIMESTAMPTZ | NULL | Lần tiếp theo dự kiến |
| `status` | VARCHAR(20) | DEFAULT 'completed' | Trạng thái thực hiện |
| `notes` | TEXT | NULL | Ghi chú |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian ghi nhận |

## Module Reporting (Báo Cáo)

### 14. Bảng `wound_reports` - Báo Cáo Vết Thương

**Mục đích**: Tạo báo cáo tổng hợp về vết thương

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID báo cáo |
| `wound_id` | UUID | FOREIGN KEY → wounds(id), NOT NULL | ID vết thương |
| `generated_by` | UUID | FOREIGN KEY → users(id), NOT NULL | Người tạo báo cáo |
| `report_type` | VARCHAR(30) | NOT NULL | Loại báo cáo |
| `report_title` | VARCHAR(200) | NOT NULL | Tiêu đề báo cáo |
| `report_period_start` | DATE | NULL | Ngày bắt đầu báo cáo |
| `report_period_end` | DATE | NULL | Ngày kết thúc báo cáo |
| `summary` | TEXT | NULL | Tóm tắt báo cáo |
| `key_findings` | JSONB | DEFAULT '[]' | Phát hiện chính |
| `recommendations` | TEXT | NULL | Khuyến nghị |
| `report_data` | JSONB | DEFAULT '{}' | Dữ liệu báo cáo chi tiết |
| `attachments` | JSONB | DEFAULT '[]' | File đính kèm |
| `status` | VARCHAR(20) | DEFAULT 'draft' | Trạng thái báo cáo |
| `shared_with` | JSONB | DEFAULT '[]' | Danh sách người được chia sẻ |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian tạo |
| `updated_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian cập nhật |

### 15. Bảng `audit_logs` - Nhật Ký Kiểm Toán

**Mục đích**: Ghi lại tất cả hoạt động trong hệ thống để tuân thủ quy định y tế

| Tên Cột | Kiểu Dữ Liệu | Ràng Buộc | Mô Tả |
|----------|--------------|-----------|--------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | ID log |
| `user_id` | UUID | FOREIGN KEY → users(id) | ID người dùng |
| `action` | VARCHAR(50) | NOT NULL | Hành động thực hiện |
| `resource_type` | VARCHAR(50) | NOT NULL | Loại tài nguyên |
| `resource_id` | UUID | NULL | ID tài nguyên |
| `old_values` | JSONB | NULL | Giá trị cũ |
| `new_values` | JSONB | NULL | Giá trị mới |
| `ip_address` | INET | NULL | Địa chỉ IP |
| `user_agent` | TEXT | NULL | Thông tin trình duyệt |
| `session_id` | UUID | NULL | ID phiên đăng nhập |
| `success` | BOOLEAN | DEFAULT true | Thành công hay không |
| `error_message` | TEXT | NULL | Thông báo lỗi (nếu có) |
| `additional_data` | JSONB | DEFAULT '{}' | Dữ liệu bổ sung |
| `created_at` | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP, NOT NULL | Thời gian thực hiện |

## Mối Quan Hệ Đầy Đủ

```
users (1) ←→ (M) user_roles (M) ←→ (1) roles
users (1) ←→ (M) sessions
users (1) ←→ (M) password_reset_tokens
users (1) ←→ (M) email_verification_tokens
users (1) ←→ (M) wounds [as patient]
users (1) ←→ (M) wound_images [as uploader]
users (1) ←→ (M) wound_assessments [as assessor]
users (1) ←→ (M) diagnoses [as doctor]
users (1) ←→ (M) treatment_plans [as creator]
users (1) ←→ (M) treatment_records [as administrator]
users (1) ←→ (M) wound_reports [as generator]
users (1) ←→ (M) audit_logs [as actor]

wounds (1) ←→ (M) wound_images
wounds (1) ←→ (M) wound_assessments
wounds (1) ←→ (M) diagnoses
wounds (1) ←→ (M) treatment_plans
wounds (1) ←→ (M) wound_reports

diagnoses (1) ←→ (M) treatment_plans
treatment_plans (1) ←→ (M) treatments
treatments (1) ←→ (M) treatment_records
```

## Tối Ưu Hóa Hiệu Suất

### Indexes Quan Trọng
```sql
-- Indexes cho tìm kiếm thường xuyên
CREATE INDEX idx_wounds_patient_status ON wounds(patient_id, status);
CREATE INDEX idx_wound_images_wound_ai ON wound_images(wound_id, ai_processed);
CREATE INDEX idx_diagnoses_wound_confirmed ON diagnoses(wound_id, is_confirmed);
CREATE INDEX idx_treatment_plans_status_dates ON treatment_plans(status, start_date, end_date);
CREATE INDEX idx_audit_logs_user_action_time ON audit_logs(user_id, action, created_at);

-- Partial indexes cho dữ liệu hoạt động
CREATE INDEX idx_active_wounds ON wounds(patient_id, created_at) WHERE status = 'active';
CREATE INDEX idx_pending_treatments ON treatments(treatment_plan_id, start_date) WHERE status = 'prescribed';
```

### Partitioning (Phân Vùng)
```sql
-- Phân vùng bảng audit_logs theo thời gian (khuyến nghị cho production)
CREATE TABLE audit_logs_2024 PARTITION OF audit_logs
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

## Bảo Mật Dữ Liệu

### Row Level Security
```sql
-- Bảo mật cấp hàng cho dữ liệu bệnh nhân
ALTER TABLE wounds ENABLE ROW LEVEL SECURITY;
CREATE POLICY wound_patient_policy ON wounds
    FOR ALL TO skinaid_app
    USING (patient_id = current_setting('app.current_user_id')::uuid
           OR current_setting('app.user_role') IN ('healthcare_provider', 'admin'));
```

### Mã Hóa Dữ Liệu Nhạy Cảm
```sql
-- Mã hóa thông tin nhạy cảm (cần extension pgcrypto)
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Ví dụ mã hóa số điện thoại
ALTER TABLE users ADD COLUMN phone_number_encrypted BYTEA;
UPDATE users SET phone_number_encrypted = pgp_sym_encrypt(phone_number, 'encryption_key');
```

---

*Tài liệu schema cơ sở dữ liệu SkinAid hoàn chỉnh với 15 bảng chính hỗ trợ đầy đủ chức năng quản lý vết thương, chẩn đoán, điều trị và báo cáo.*
