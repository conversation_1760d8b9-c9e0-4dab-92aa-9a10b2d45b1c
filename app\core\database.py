from sqlmodel import SQLModel
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, async_sessionmaker, AsyncSession
from app.core.config import settings
from contextlib import asynccontextmanager 
from typing import AsyncGenerator

async_engine: AsyncEngine = create_async_engine(
    url= settings.DB_URL, 
    echo=True, 
    future=True,
    pool_pre_ping=True,
)

async_session_maker = async_sessionmaker(
    bind= async_engine, 
    autoflush= False, 
    autocommit= False,
    expire_on_commit= False, 
    class_ = AsyncSession
)

@asynccontextmanager 
async def get_db() -> AsyncGenerator[AsyncSession, None]: 
    session: AsyncSession = async_session_maker()
    try: 
        yield session
        await session.commit() 
    except: 
        await session.rollback()
        raise
    finally: 
        await session.close() 


async def get_session()-> AsyncGenerator[AsyncSession, None]: 
    async with async_session_maker() as session: 
        yield session

async def init_db() -> None: 
    async with async_engine.begin() as conn: 
        await conn.run_sync(SQLModel.metadata.create_all)

