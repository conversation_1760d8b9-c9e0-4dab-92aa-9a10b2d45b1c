# Authentication System Implementation Guide

## FastAPI Integration and Practical Usage

### Database Setup and Configuration

#### 1. Database Connection Setup

```python
# Modular/core/database.py
"""
Database configuration and session management for SkinAid authentication system.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import Static<PERSON>ool
import os
from typing import Generator

# Database URL from environment variables
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql://username:password@localhost:5432/skinaid_db"
)

# Create SQLAlchemy engine with PostgreSQL optimizations
engine = create_engine(
    DATABASE_URL,
    # Connection pool settings for production
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=3600,   # Recycle connections every hour
    echo=False,          # Set to True for SQL debugging
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models (imported from models file)
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session for FastAPI.
    
    Usage in FastAPI endpoints:
    @app.get("/users/")
    def get_users(db: Session = Depends(get_db)):
        return db.query(User).all()
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Create all tables in the database."""
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """Drop all tables (use with caution!)."""
    Base.metadata.drop_all(bind=engine)
```

#### 2. Authentication Service Layer

```python
# Modular/service/auth_service.py
"""
Authentication service layer with business logic for user management,
password handling, and session management.
"""

import secrets
import hashlib
from datetime import datetime, timedelta, timezone
from typing import Optional, List, Dict, Any
from uuid import uuid4

import bcrypt
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..models.auth_models import User, Role, UserRole, Session as UserSession
from ..models.auth_models import PasswordResetToken, EmailVerificationToken

class AuthService:
    """
    Authentication service providing core authentication functionality.
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    # Password Management
    @staticmethod
    def hash_password(password: str) -> str:
        """
        Hash a password using bcrypt with salt.
        
        Args:
            password: Plain text password
            
        Returns:
            Hashed password string
        """
        # Generate salt and hash password
        salt = bcrypt.gensalt(rounds=12)  # 12 rounds for good security/performance balance
        password_bytes = password.encode('utf-8')
        hashed = bcrypt.hashpw(password_bytes, salt)
        return hashed.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash.
        
        Args:
            password: Plain text password to verify
            hashed_password: Stored hash to verify against
            
        Returns:
            True if password matches, False otherwise
        """
        try:
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except Exception:
            return False
    
    # User Management
    def create_user(
        self, 
        username: str, 
        email: str, 
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        phone_number: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> User:
        """
        Create a new user account.
        
        Args:
            username: Unique username
            email: User's email address
            password: Plain text password (will be hashed)
            first_name: User's first name
            last_name: User's last name
            phone_number: User's phone number
            metadata: Additional user metadata
            
        Returns:
            Created User object
            
        Raises:
            ValueError: If username or email already exists
        """
        # Check if user already exists
        existing_user = self.db.query(User).filter(
            or_(User.username == username, User.email == email)
        ).first()
        
        if existing_user:
            if existing_user.username == username:
                raise ValueError("Username already exists")
            else:
                raise ValueError("Email already exists")
        
        # Create new user
        user = User(
            username=username,
            email=email,
            password_hash=self.hash_password(password),
            first_name=first_name,
            last_name=last_name,
            phone_number=phone_number,
            metadata=metadata or {},
            account_status="pending"  # Requires email verification
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def authenticate_user(self, username_or_email: str, password: str) -> Optional[User]:
        """
        Authenticate a user with username/email and password.
        
        Args:
            username_or_email: Username or email address
            password: Plain text password
            
        Returns:
            User object if authentication successful, None otherwise
        """
        # Find user by username or email
        user = self.db.query(User).filter(
            or_(User.username == username_or_email, User.email == username_or_email)
        ).first()
        
        if not user:
            return None
        
        # Check if account is locked
        if user.is_locked:
            return None
        
        # Verify password
        if not self.verify_password(password, user.password_hash):
            # Increment failed login attempts
            user.failed_login_attempts += 1
            
            # Lock account after 5 failed attempts for 30 minutes
            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)
            
            self.db.commit()
            return None
        
        # Successful login - reset failed attempts and update last login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.now(timezone.utc)
        self.db.commit()
        
        return user
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()
    
    # Role Management
    def assign_role_to_user(
        self, 
        user_id: str, 
        role_name: str, 
        assigned_by_id: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> UserRole:
        """
        Assign a role to a user.
        
        Args:
            user_id: ID of user to assign role to
            role_name: Name of role to assign
            assigned_by_id: ID of user assigning the role
            expires_at: Optional expiration date for role
            
        Returns:
            UserRole object
            
        Raises:
            ValueError: If user or role not found, or role already assigned
        """
        # Get user and role
        user = self.get_user_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        role = self.db.query(Role).filter(Role.name == role_name).first()
        if not role:
            raise ValueError("Role not found")
        
        # Check if role already assigned
        existing_assignment = self.db.query(UserRole).filter(
            and_(
                UserRole.user_id == user_id,
                UserRole.role_id == role.id,
                UserRole.is_active == True
            )
        ).first()
        
        if existing_assignment:
            raise ValueError("Role already assigned to user")
        
        # Create role assignment
        user_role = UserRole(
            user_id=user_id,
            role_id=role.id,
            assigned_by=assigned_by_id,
            expires_at=expires_at
        )
        
        self.db.add(user_role)
        self.db.commit()
        self.db.refresh(user_role)
        
        return user_role
    
    def get_user_permissions(self, user_id: str) -> List[str]:
        """
        Get all permissions for a user based on their active roles.
        
        Args:
            user_id: User ID
            
        Returns:
            List of permission strings
        """
        # Get all active roles for user
        user_roles = self.db.query(UserRole).join(Role).filter(
            and_(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                Role.is_active == True,
                or_(
                    UserRole.expires_at.is_(None),
                    UserRole.expires_at > datetime.now(timezone.utc)
                )
            )
        ).all()
        
        # Collect all permissions
        permissions = set()
        for user_role in user_roles:
            permissions.update(user_role.role.permissions)
        
        return list(permissions)
    
    def user_has_permission(self, user_id: str, permission: str) -> bool:
        """
        Check if a user has a specific permission.
        
        Args:
            user_id: User ID
            permission: Permission string to check
            
        Returns:
            True if user has permission, False otherwise
        """
        user_permissions = self.get_user_permissions(user_id)
        return permission in user_permissions
```

### Session Management

```python
# Modular/service/session_service.py
"""
Session management service for handling user sessions and JWT tokens.
"""

import jwt
import secrets
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session as DBSession
from sqlalchemy import and_

from ..models.auth_models import User, Session as UserSession

class SessionService:
    """Service for managing user sessions and JWT tokens."""
    
    def __init__(self, db: DBSession, secret_key: str):
        self.db = db
        self.secret_key = secret_key
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 7
    
    def create_session(
        self, 
        user: User, 
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """
        Create a new session for a user.
        
        Args:
            user: User object
            ip_address: Client IP address
            user_agent: Client user agent
            device_info: Additional device information
            
        Returns:
            Dictionary with access_token and refresh_token
        """
        # Generate tokens
        access_token = self._create_access_token(user.id)
        refresh_token = self._generate_refresh_token()
        
        # Calculate expiration times
        access_expires = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire_minutes)
        refresh_expires = datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire_days)
        
        # Create session record
        session = UserSession(
            user_id=user.id,
            session_token=access_token,
            refresh_token=refresh_token,
            expires_at=refresh_expires,  # Session expires when refresh token expires
            ip_address=ip_address,
            user_agent=user_agent,
            device_info=device_info or {}
        )
        
        self.db.add(session)
        self.db.commit()
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60
        }
    
    def _create_access_token(self, user_id: str) -> str:
        """Create JWT access token."""
        expire = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire_minutes)
        payload = {
            "sub": user_id,
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "type": "access"
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _generate_refresh_token(self) -> str:
        """Generate secure refresh token."""
        return secrets.token_urlsafe(32)
    
    def verify_access_token(self, token: str) -> Optional[str]:
        """
        Verify JWT access token and return user ID.
        
        Args:
            token: JWT access token
            
        Returns:
            User ID if token is valid, None otherwise
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            user_id = payload.get("sub")
            token_type = payload.get("type")
            
            if user_id is None or token_type != "access":
                return None
                
            return user_id
        except jwt.PyJWTError:
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, str]]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            New token dictionary or None if refresh token invalid
        """
        # Find session with refresh token
        session = self.db.query(UserSession).filter(
            and_(
                UserSession.refresh_token == refresh_token,
                UserSession.is_active == True,
                UserSession.expires_at > datetime.now(timezone.utc)
            )
        ).first()
        
        if not session:
            return None
        
        # Generate new access token
        new_access_token = self._create_access_token(session.user_id)
        
        # Update session
        session.session_token = new_access_token
        session.last_accessed = datetime.now(timezone.utc)
        self.db.commit()
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60
        }
    
    def revoke_session(self, session_id: str, revoked_by_id: Optional[str] = None, reason: str = "user_logout"):
        """Revoke a specific session."""
        session = self.db.query(UserSession).filter(UserSession.id == session_id).first()
        if session:
            session.is_active = False
            session.revoked_at = datetime.now(timezone.utc)
            session.revoked_by = revoked_by_id
            session.revoke_reason = reason
            self.db.commit()
    
    def revoke_all_user_sessions(self, user_id: str, except_session_id: Optional[str] = None):
        """Revoke all sessions for a user (except optionally one session)."""
        query = self.db.query(UserSession).filter(
            and_(
                UserSession.user_id == user_id,
                UserSession.is_active == True
            )
        )
        
        if except_session_id:
            query = query.filter(UserSession.id != except_session_id)
        
        sessions = query.all()
        for session in sessions:
            session.is_active = False
            session.revoked_at = datetime.now(timezone.utc)
            session.revoke_reason = "revoke_all_sessions"
        
        self.db.commit()
```

---

*This implementation guide provides practical examples for integrating the authentication models with FastAPI. The next section will cover FastAPI endpoints and middleware.*
