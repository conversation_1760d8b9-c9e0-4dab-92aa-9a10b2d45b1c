# Authentication Security Best Practices and Deployment Guide

## Security Implementation Guidelines

### 1. Environment Configuration

```python
# Modular/core/config.py
"""
Configuration management with security best practices.
"""

from pydantic import BaseSettings, validator
from typing import Optional
import secrets

class Settings(BaseSettings):
    """Application settings with security configurations."""
    
    # Database Configuration
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    
    # Security Configuration
    SECRET_KEY: str = secrets.token_urlsafe(32)  # Generate if not provided
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Password Security
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_DIGITS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = False
    BCRYPT_ROUNDS: int = 12
    
    # Account Security
    MAX_LOGIN_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_MINUTES: int = 30
    EMAIL_VERIFICATION_EXPIRE_HOURS: int = 24
    PASSWORD_RESET_EXPIRE_HOURS: int = 1
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 60
    LOGIN_RATE_LIMIT_PER_MINUTE: int = 5
    
    # CORS Configuration
    CORS_ORIGINS: list = ["http://localhost:3000", "https://yourdomain.com"]
    CORS_ALLOW_CREDENTIALS: bool = True
    
    # Email Configuration (for verification and password reset)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_USE_TLS: bool = True
    FROM_EMAIL: Optional[str] = None
    
    # Healthcare Compliance
    AUDIT_LOG_RETENTION_DAYS: int = 2555  # 7 years for healthcare compliance
    SESSION_TIMEOUT_MINUTES: int = 30
    FORCE_PASSWORD_CHANGE_DAYS: int = 90
    
    @validator('SECRET_KEY')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters long')
        return v
    
    @validator('DATABASE_URL')
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'postgresql+psycopg2://')):
            raise ValueError('DATABASE_URL must be a PostgreSQL connection string')
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
_settings = None

def get_settings() -> Settings:
    """Get application settings (singleton pattern)."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
```

### 2. Security Middleware

```python
# Modular/middlewares/security_middleware.py
"""
Security middleware for FastAPI application.
"""

import time
import logging
from fastapi import Request, Response, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, DefaultDict

logger = logging.getLogger(__name__)

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Security middleware implementing rate limiting, request logging,
    and security headers.
    """
    
    def __init__(self, app, rate_limit_requests: int = 60, rate_limit_window: int = 60):
        super().__init__(app)
        self.rate_limit_requests = rate_limit_requests
        self.rate_limit_window = rate_limit_window
        
        # In-memory rate limiting (use Redis in production)
        self.request_counts: DefaultDict[str, list] = defaultdict(list)
        self.login_attempts: DefaultDict[str, list] = defaultdict(list)
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process request through security middleware."""
        start_time = time.time()
        
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Apply rate limiting
        if self._is_rate_limited(client_ip, request.url.path):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        
        # Log request
        self._log_request(request, client_ip)
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        self._add_security_headers(response)
        
        # Log response
        process_time = time.time() - start_time
        self._log_response(request, response, process_time, client_ip)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, client_ip: str, path: str) -> bool:
        """Check if client is rate limited."""
        now = datetime.now()
        window_start = now - timedelta(seconds=self.rate_limit_window)
        
        # Clean old requests
        self.request_counts[client_ip] = [
            req_time for req_time in self.request_counts[client_ip]
            if req_time > window_start
        ]
        
        # Check general rate limit
        if len(self.request_counts[client_ip]) >= self.rate_limit_requests:
            return True
        
        # Special rate limiting for login endpoints
        if "/auth/login" in path:
            self.login_attempts[client_ip] = [
                req_time for req_time in self.login_attempts[client_ip]
                if req_time > window_start
            ]
            
            if len(self.login_attempts[client_ip]) >= 5:  # Max 5 login attempts per minute
                return True
            
            self.login_attempts[client_ip].append(now)
        
        # Add request to count
        self.request_counts[client_ip].append(now)
        return False
    
    def _add_security_headers(self, response: Response) -> None:
        """Add security headers to response."""
        # Prevent clickjacking
        response.headers["X-Frame-Options"] = "DENY"
        
        # Prevent MIME type sniffing
        response.headers["X-Content-Type-Options"] = "nosniff"
        
        # XSS protection
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # Referrer policy
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Content Security Policy
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        
        # HSTS (only in production with HTTPS)
        # response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    def _log_request(self, request: Request, client_ip: str) -> None:
        """Log incoming request for audit trail."""
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"from {client_ip} "
            f"User-Agent: {request.headers.get('user-agent', 'unknown')}"
        )
    
    def _log_response(self, request: Request, response: Response, process_time: float, client_ip: str) -> None:
        """Log response for audit trail."""
        logger.info(
            f"Response: {request.method} {request.url.path} "
            f"Status: {response.status_code} "
            f"Time: {process_time:.3f}s "
            f"IP: {client_ip}"
        )

class AuditLogMiddleware(BaseHTTPMiddleware):
    """
    Audit logging middleware for healthcare compliance.
    Logs all authentication-related activities.
    """
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Log authentication events for audit trail."""
        
        # Check if this is an auth-related endpoint
        if "/auth/" in str(request.url.path):
            await self._log_auth_event(request, "request")
        
        response = await call_next(request)
        
        # Log auth responses
        if "/auth/" in str(request.url.path):
            await self._log_auth_event(request, "response", response.status_code)
        
        return response
    
    async def _log_auth_event(self, request: Request, event_type: str, status_code: int = None):
        """Log authentication event for audit trail."""
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "endpoint": str(request.url.path),
            "method": request.method,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "status_code": status_code
        }
        
        # In production, send to dedicated audit log system
        logger.info(f"AUDIT: {log_data}")
```

### 3. Database Security Configuration

```sql
-- Database security configuration for PostgreSQL

-- Create dedicated database user for the application
CREATE USER skinaid_app WITH PASSWORD 'secure_random_password_here';

-- Grant only necessary permissions
GRANT CONNECT ON DATABASE skinaid_db TO skinaid_app;
GRANT USAGE ON SCHEMA public TO skinaid_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO skinaid_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO skinaid_app;

-- Enable row-level security for sensitive tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_reset_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_verification_tokens ENABLE ROW LEVEL SECURITY;

-- Create policies for row-level security (example for users table)
CREATE POLICY user_own_data ON users
    FOR ALL
    TO skinaid_app
    USING (id = current_setting('app.current_user_id')::uuid);

-- Enable audit logging
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create audit log table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (table_name, operation, old_values, user_id)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), OLD.id);
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (table_name, operation, old_values, new_values, user_id)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), row_to_json(NEW), NEW.id);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (table_name, operation, new_values, user_id)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW), NEW.id);
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER users_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER sessions_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON sessions
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

### 4. Production Deployment Checklist

#### Environment Security
- [ ] Use strong, randomly generated SECRET_KEY (minimum 32 characters)
- [ ] Store sensitive configuration in environment variables, not code
- [ ] Use separate databases for development, staging, and production
- [ ] Enable SSL/TLS for database connections
- [ ] Configure firewall to restrict database access

#### Application Security
- [ ] Enable HTTPS in production (use Let's Encrypt or commercial SSL)
- [ ] Configure CORS properly for your frontend domains
- [ ] Set up rate limiting (consider using Redis for distributed rate limiting)
- [ ] Enable security headers middleware
- [ ] Configure proper logging and monitoring

#### Database Security
- [ ] Create dedicated database user with minimal permissions
- [ ] Enable row-level security where appropriate
- [ ] Set up database backups with encryption
- [ ] Configure audit logging for compliance
- [ ] Regularly update PostgreSQL and apply security patches

#### Monitoring and Alerting
- [ ] Set up application performance monitoring (APM)
- [ ] Configure alerts for failed login attempts
- [ ] Monitor for unusual access patterns
- [ ] Set up log aggregation and analysis
- [ ] Configure health checks and uptime monitoring

#### Compliance (Healthcare Specific)
- [ ] Implement audit logging for all data access
- [ ] Set up data retention policies
- [ ] Configure encrypted backups
- [ ] Document security procedures
- [ ] Regular security assessments and penetration testing

### 5. Testing Security Implementation

```python
# tests/test_auth_security.py
"""
Security tests for authentication system.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

def test_password_hashing():
    """Test password hashing security."""
    from Modular.service.auth_service import AuthService
    
    password = "TestPassword123!"
    hashed = AuthService.hash_password(password)
    
    # Verify password can be verified
    assert AuthService.verify_password(password, hashed)
    
    # Verify wrong password fails
    assert not AuthService.verify_password("WrongPassword", hashed)
    
    # Verify hash is different each time (salt)
    hashed2 = AuthService.hash_password(password)
    assert hashed != hashed2

def test_rate_limiting(client: TestClient):
    """Test rate limiting functionality."""
    # Make multiple requests quickly
    for i in range(10):
        response = client.post("/auth/login", json={
            "username_or_email": "<EMAIL>",
            "password": "wrongpassword"
        })
    
    # Should eventually get rate limited
    response = client.post("/auth/login", json={
        "username_or_email": "<EMAIL>",
        "password": "wrongpassword"
    })
    assert response.status_code == 429

def test_account_lockout(client: TestClient, db: Session):
    """Test account lockout after failed attempts."""
    # Create test user
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    client.post("/auth/register", json=user_data)
    
    # Make multiple failed login attempts
    for i in range(6):
        response = client.post("/auth/login", json={
            "username_or_email": "<EMAIL>",
            "password": "wrongpassword"
        })
    
    # Account should be locked
    response = client.post("/auth/login", json={
        "username_or_email": "<EMAIL>",
        "password": "TestPassword123!"  # Correct password
    })
    assert response.status_code == 401

def test_jwt_token_security(client: TestClient):
    """Test JWT token security."""
    # Register and login user
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    client.post("/auth/register", json=user_data)
    
    login_response = client.post("/auth/login", json={
        "username_or_email": "<EMAIL>",
        "password": "TestPassword123!"
    })
    
    token = login_response.json()["access_token"]
    
    # Test with valid token
    response = client.get("/auth/profile", headers={
        "Authorization": f"Bearer {token}"
    })
    assert response.status_code == 200
    
    # Test with invalid token
    response = client.get("/auth/profile", headers={
        "Authorization": "Bearer invalid_token"
    })
    assert response.status_code == 401
    
    # Test without token
    response = client.get("/auth/profile")
    assert response.status_code == 401
```

---

*This comprehensive security guide covers all aspects of securing the authentication system for production deployment in a healthcare environment.*
