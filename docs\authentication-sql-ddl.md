# Authentication System - PostgreSQL DDL Statements

## Database Schema Creation

### 1. Users Table

```sql
-- Users table: Core user information and authentication credentials
CREATE TABLE users (
    -- Primary key: UUID for security and scalability
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Authentication fields
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL, -- bcrypt hash, never store plain text
    
    -- User profile information
    first_name VARCHAR(100),
    last_name VA<PERSON>HA<PERSON>(100),
    phone_number VARCHAR(20),
    
    -- Account status and verification
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_verified BOOLEAN DEFAULT false NOT NULL,
    is_superuser BOOLEAN DEFAULT false NOT NULL,
    
    -- Account status enum for more granular control
    account_status VARCHAR(20) DEFAULT 'pending' NOT NULL 
        CHECK (account_status IN ('pending', 'active', 'suspended', 'deactivated')),
    
    -- Security and audit fields
    last_login TIMESTAMPTZ,
    failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
    locked_until TIMESTAMPTZ,
    
    -- Metadata for extensibility (healthcare-specific data)
    metadata JSONB DEFAULT '{}',
    
    -- Audit timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Indexes for performance optimization
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active_verified ON users(is_active, is_verified) WHERE is_active = true;
CREATE INDEX idx_users_account_status ON users(account_status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- GIN index for JSONB metadata queries
CREATE INDEX idx_users_metadata ON users USING GIN(metadata);

-- Email format validation constraint
ALTER TABLE users ADD CONSTRAINT chk_users_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Username format constraint (alphanumeric and underscore only)
ALTER TABLE users ADD CONSTRAINT chk_users_username_format 
    CHECK (username ~* '^[A-Za-z0-9_]{3,50}$');
```

### 2. Roles Table

```sql
-- Roles table: Define different user roles in the system
CREATE TABLE roles (
    -- Primary key: UUID for consistency
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Role identification
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Role hierarchy and permissions
    level INTEGER DEFAULT 0 NOT NULL, -- Higher number = more permissions
    permissions JSONB DEFAULT '[]', -- Array of permission strings
    
    -- Role status
    is_active BOOLEAN DEFAULT true NOT NULL,
    
    -- Audit timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Insert default roles for healthcare system
INSERT INTO roles (name, display_name, description, level, permissions) VALUES
('patient', 'Patient', 'Basic user who can manage their own wound data', 1, 
 '["view_own_wounds", "create_wound_report", "view_own_profile", "update_own_profile"]'),
('healthcare_provider', 'Healthcare Provider', 'Medical professional who can manage patient data', 5, 
 '["view_patient_wounds", "create_diagnosis", "update_wound_status", "view_patient_profiles", "create_treatment_plan"]'),
('admin', 'Administrator', 'System administrator with full access', 10, 
 '["manage_users", "manage_roles", "view_all_data", "system_configuration", "audit_logs"]');

-- Indexes
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_active ON roles(is_active) WHERE is_active = true;
CREATE INDEX idx_roles_level ON roles(level);
CREATE INDEX idx_roles_permissions ON roles USING GIN(permissions);
```

### 3. User_Roles Junction Table

```sql
-- User_Roles table: Many-to-many relationship between users and roles
CREATE TABLE user_roles (
    -- Composite primary key
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    
    -- Assignment metadata
    assigned_by UUID REFERENCES users(id), -- Who assigned this role
    assigned_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at TIMESTAMPTZ, -- Optional role expiration
    
    -- Status
    is_active BOOLEAN DEFAULT true NOT NULL,
    
    -- Additional metadata for role assignment
    metadata JSONB DEFAULT '{}',
    
    -- Primary key constraint
    PRIMARY KEY (user_id, role_id)
);

-- Indexes for efficient queries
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_user_roles_active ON user_roles(is_active) WHERE is_active = true;
CREATE INDEX idx_user_roles_expires_at ON user_roles(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_user_roles_assigned_by ON user_roles(assigned_by);
```

### 4. Sessions Table

```sql
-- Sessions table: Manage user authentication sessions and JWT tokens
CREATE TABLE sessions (
    -- Primary key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User reference
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Session identification
    session_token TEXT UNIQUE NOT NULL, -- JWT token or session ID
    refresh_token TEXT UNIQUE, -- For token refresh mechanism
    
    -- Session metadata
    device_info JSONB DEFAULT '{}', -- Browser, OS, device type
    ip_address INET, -- Client IP address
    user_agent TEXT, -- Browser user agent string
    
    -- Session timing
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    last_accessed TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Session status
    is_active BOOLEAN DEFAULT true NOT NULL,
    revoked_at TIMESTAMPTZ,
    revoked_by UUID REFERENCES users(id), -- Who revoked the session
    revoke_reason VARCHAR(100) -- Reason for revocation
);

-- Indexes for session management
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_session_token ON sessions(session_token);
CREATE INDEX idx_sessions_refresh_token ON sessions(refresh_token) WHERE refresh_token IS NOT NULL;
CREATE INDEX idx_sessions_active ON sessions(is_active, expires_at) WHERE is_active = true;
CREATE INDEX idx_sessions_ip_address ON sessions(ip_address);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);

-- Partial index for active sessions only (performance optimization)
CREATE INDEX idx_sessions_active_user ON sessions(user_id, last_accessed) 
    WHERE is_active = true AND expires_at > CURRENT_TIMESTAMP;
```

### 5. Password Reset Tokens Table

```sql
-- Password_Reset_Tokens table: Secure password reset functionality
CREATE TABLE password_reset_tokens (
    -- Primary key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User reference
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Token information
    token TEXT UNIQUE NOT NULL, -- Secure random token
    token_hash TEXT NOT NULL, -- Hashed version for database storage
    
    -- Token timing and usage
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL, -- Typically 1-24 hours
    used_at TIMESTAMPTZ, -- When token was used
    
    -- Security metadata
    ip_address INET, -- IP that requested reset
    user_agent TEXT, -- Browser that requested reset
    
    -- Token status
    is_used BOOLEAN DEFAULT false NOT NULL,
    is_revoked BOOLEAN DEFAULT false NOT NULL
);

-- Indexes
CREATE INDEX idx_password_reset_tokens_user_id ON password_reset_tokens(user_id);
CREATE INDEX idx_password_reset_tokens_token_hash ON password_reset_tokens(token_hash);
CREATE INDEX idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);

-- Partial index for active tokens only
CREATE INDEX idx_password_reset_tokens_active ON password_reset_tokens(user_id, expires_at) 
    WHERE is_used = false AND is_revoked = false AND expires_at > CURRENT_TIMESTAMP;

-- Constraint to ensure token expiration is reasonable (max 24 hours)
ALTER TABLE password_reset_tokens ADD CONSTRAINT chk_password_reset_token_expiry 
    CHECK (expires_at <= created_at + INTERVAL '24 hours');
```

### 6. Email Verification Tokens Table

```sql
-- Email_Verification_Tokens table: Email verification workflow
CREATE TABLE email_verification_tokens (
    -- Primary key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User reference
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Email being verified (supports email change workflow)
    email VARCHAR(255) NOT NULL,
    
    -- Token information
    token TEXT UNIQUE NOT NULL, -- Secure random token
    token_hash TEXT NOT NULL, -- Hashed version for database storage
    
    -- Token timing and usage
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL, -- Typically 24-72 hours
    verified_at TIMESTAMPTZ, -- When email was verified
    
    -- Token type for different verification scenarios
    token_type VARCHAR(20) DEFAULT 'registration' NOT NULL 
        CHECK (token_type IN ('registration', 'email_change', 'reactivation')),
    
    -- Security metadata
    ip_address INET,
    user_agent TEXT,
    
    -- Token status
    is_used BOOLEAN DEFAULT false NOT NULL,
    is_revoked BOOLEAN DEFAULT false NOT NULL
);

-- Indexes
CREATE INDEX idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX idx_email_verification_tokens_email ON email_verification_tokens(email);
CREATE INDEX idx_email_verification_tokens_token_hash ON email_verification_tokens(token_hash);
CREATE INDEX idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);
CREATE INDEX idx_email_verification_tokens_type ON email_verification_tokens(token_type);

-- Partial index for active tokens
CREATE INDEX idx_email_verification_tokens_active ON email_verification_tokens(user_id, email) 
    WHERE is_used = false AND is_revoked = false AND expires_at > CURRENT_TIMESTAMP;

-- Constraint for reasonable expiration (max 7 days)
ALTER TABLE email_verification_tokens ADD CONSTRAINT chk_email_verification_token_expiry 
    CHECK (expires_at <= created_at + INTERVAL '7 days');
```

### Database Functions and Triggers

```sql
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic updated_at maintenance
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired tokens (call via cron job)
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Clean up expired password reset tokens
    DELETE FROM password_reset_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Clean up expired email verification tokens
    DELETE FROM email_verification_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
    
    -- Clean up old revoked sessions
    DELETE FROM sessions 
    WHERE is_active = false AND revoked_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
```

## Performance Considerations

### Query Optimization
- **Partial indexes**: Only index active/valid records
- **Composite indexes**: For common query patterns
- **GIN indexes**: For JSONB column searches

### Maintenance
- **Regular cleanup**: Expired tokens and old sessions
- **Index maintenance**: Monitor and rebuild as needed
- **Statistics updates**: Keep query planner informed

### Security
- **Row-level security**: Can be added for multi-tenant scenarios
- **Audit logging**: Consider separate audit table for compliance
- **Encryption**: Consider column-level encryption for sensitive data

---

*These DDL statements create a robust, secure authentication system optimized for PostgreSQL and healthcare applications.*
