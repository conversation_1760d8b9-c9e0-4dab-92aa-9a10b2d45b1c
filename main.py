from fastapi import FastAPI
from app.Modular.auth_module.api.v1.endpoints import router as auth_router
from contextlib import asynccontextmanager
from app.core.database import init_db

@asynccontextmanager
async def life_span(app:FastAPI):
    print("Server is start")
    await init_db()
    yield
    print("Server has been stopped")

version = "v1"
app = FastAPI(
    title ="SkinAid", 
    version= version, 
    lifespan= life_span 
) 

app.include_router(auth_router, prefix=f"/api/{version}")