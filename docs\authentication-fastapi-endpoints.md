# FastAPI Authentication Endpoints and Middleware

## FastAPI Integration with Authentication System

### 1. Pydantic Schemas for Request/Response

```python
# Modular/schemas/auth_schemas.py
"""
Pydantic schemas for authentication API requests and responses.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, EmailStr, Field, validator
import re

# User Registration and Authentication Schemas
class UserRegistrationRequest(BaseModel):
    """Schema for user registration request."""
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., min_length=8, max_length=128, description="User's password")
    first_name: Optional[str] = Field(None, max_length=100, description="User's first name")
    last_name: Optional[str] = Field(None, max_length=100, description="User's last name")
    phone_number: Optional[str] = Field(None, max_length=20, description="User's phone number")
    
    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[A-Za-z0-9_]{3,50}$', v):
            raise ValueError('Username must contain only letters, numbers, and underscores')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserLoginRequest(BaseModel):
    """Schema for user login request."""
    username_or_email: str = Field(..., description="Username or email address")
    password: str = Field(..., description="User's password")

class TokenResponse(BaseModel):
    """Schema for authentication token response."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class RefreshTokenRequest(BaseModel):
    """Schema for token refresh request."""
    refresh_token: str

# User Profile Schemas
class UserProfileResponse(BaseModel):
    """Schema for user profile response."""
    id: str
    username: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    phone_number: Optional[str]
    is_active: bool
    is_verified: bool
    account_status: str
    roles: List[str]
    created_at: datetime
    last_login: Optional[datetime]
    
    class Config:
        from_attributes = True

class UserUpdateRequest(BaseModel):
    """Schema for updating user profile."""
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone_number: Optional[str] = Field(None, max_length=20)

# Password Management Schemas
class PasswordChangeRequest(BaseModel):
    """Schema for password change request."""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v

class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)

# Email Verification Schemas
class EmailVerificationRequest(BaseModel):
    """Schema for email verification request."""
    token: str

# Role Management Schemas
class RoleResponse(BaseModel):
    """Schema for role response."""
    id: str
    name: str
    display_name: str
    description: Optional[str]
    level: int
    permissions: List[str]
    
    class Config:
        from_attributes = True

class AssignRoleRequest(BaseModel):
    """Schema for role assignment request."""
    user_id: str
    role_name: str
    expires_at: Optional[datetime] = None
```

### 2. Authentication Dependencies

```python
# Modular/core/dependencies.py
"""
FastAPI dependencies for authentication and authorization.
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional

from .database import get_db
from ..service.auth_service import AuthService
from ..service.session_service import SessionService
from ..models.auth_models import User
from .config import get_settings

# Security scheme for JWT tokens
security = HTTPBearer()

def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """Get authentication service instance."""
    return AuthService(db)

def get_session_service(db: Session = Depends(get_db)) -> SessionService:
    """Get session service instance."""
    settings = get_settings()
    return SessionService(db, settings.SECRET_KEY)

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session_service: SessionService = Depends(get_session_service),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """
    Get current authenticated user from JWT token.
    
    This dependency extracts the JWT token from the Authorization header,
    verifies it, and returns the corresponding User object.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Extract token from credentials
        token = credentials.credentials
        
        # Verify token and get user ID
        user_id = session_service.verify_access_token(token)
        if user_id is None:
            raise credentials_exception
        
        # Get user from database
        user = auth_service.get_user_by_id(user_id)
        if user is None:
            raise credentials_exception
        
        # Check if user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is inactive"
            )
        
        return user
        
    except Exception:
        raise credentials_exception

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active and verified user.
    
    This dependency ensures the user is both active and email-verified.
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email not verified"
        )
    
    return current_user

def require_permission(permission: str):
    """
    Create a dependency that requires a specific permission.
    
    Usage:
    @app.get("/admin/users")
    def get_all_users(
        current_user: User = Depends(require_permission("manage_users"))
    ):
        ...
    """
    async def permission_dependency(
        current_user: User = Depends(get_current_active_user),
        auth_service: AuthService = Depends(get_auth_service)
    ) -> User:
        if not auth_service.user_has_permission(current_user.id, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    
    return permission_dependency

def require_role(role_name: str):
    """
    Create a dependency that requires a specific role.
    
    Usage:
    @app.get("/healthcare/patients")
    def get_patients(
        current_user: User = Depends(require_role("healthcare_provider"))
    ):
        ...
    """
    async def role_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        user_roles = current_user.get_roles()
        if role_name not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role_name}' required"
            )
        return current_user
    
    return role_dependency
```

### 3. Authentication Endpoints

```python
# Modular/api/v1/auth.py
"""
Authentication endpoints for user registration, login, and profile management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Dict, Any

from ...core.database import get_db
from ...core.dependencies import (
    get_auth_service, get_session_service, get_current_user, 
    get_current_active_user, require_permission
)
from ...service.auth_service import AuthService
from ...service.session_service import SessionService
from ...schemas.auth_schemas import (
    UserRegistrationRequest, UserLoginRequest, TokenResponse,
    RefreshTokenRequest, UserProfileResponse, UserUpdateRequest,
    PasswordChangeRequest, PasswordResetRequest, PasswordResetConfirm
)
from ...models.auth_models import User

router = APIRouter(prefix="/auth", tags=["authentication"])

@router.post("/register", response_model=Dict[str, str], status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserRegistrationRequest,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user account.
    
    Creates a new user account with the provided information.
    The account will be in 'pending' status until email verification.
    """
    try:
        user = auth_service.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            phone_number=user_data.phone_number
        )
        
        # Assign default 'patient' role to new users
        auth_service.assign_role_to_user(user.id, "patient")
        
        # TODO: Send email verification email
        # email_service.send_verification_email(user.email, verification_token)
        
        return {
            "message": "User registered successfully",
            "user_id": user.id,
            "email_verification_required": True
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=TokenResponse)
async def login_user(
    login_data: UserLoginRequest,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service),
    session_service: SessionService = Depends(get_session_service)
):
    """
    Authenticate user and return access tokens.
    
    Validates user credentials and returns JWT access token and refresh token.
    """
    # Authenticate user
    user = auth_service.authenticate_user(
        login_data.username_or_email, 
        login_data.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username/email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Check account status
    if user.account_status != "active":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Account is {user.account_status}. Please contact support."
        )
    
    # Create session and tokens
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent")
    
    tokens = session_service.create_session(
        user=user,
        ip_address=client_ip,
        user_agent=user_agent
    )
    
    return TokenResponse(**tokens)

@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    session_service: SessionService = Depends(get_session_service)
):
    """
    Refresh access token using refresh token.
    
    Exchanges a valid refresh token for a new access token.
    """
    tokens = session_service.refresh_access_token(refresh_data.refresh_token)
    
    if not tokens:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    return TokenResponse(**tokens)

@router.post("/logout")
async def logout_user(
    current_user: User = Depends(get_current_user),
    session_service: SessionService = Depends(get_session_service)
):
    """
    Logout current user and revoke session.
    
    Revokes the current session, making the access token invalid.
    """
    # TODO: Get current session ID from token and revoke it
    # For now, we'll revoke all sessions for the user
    session_service.revoke_all_user_sessions(current_user.id)
    
    return {"message": "Successfully logged out"}

@router.get("/profile", response_model=UserProfileResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get current user's profile information.
    
    Returns detailed profile information for the authenticated user.
    """
    return UserProfileResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        phone_number=current_user.phone_number,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        account_status=current_user.account_status,
        roles=current_user.get_roles(),
        created_at=current_user.created_at,
        last_login=current_user.last_login
    )

@router.put("/profile", response_model=UserProfileResponse)
async def update_user_profile(
    profile_data: UserUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's profile information.
    
    Allows users to update their profile information.
    """
    # Update user fields
    if profile_data.first_name is not None:
        current_user.first_name = profile_data.first_name
    if profile_data.last_name is not None:
        current_user.last_name = profile_data.last_name
    if profile_data.phone_number is not None:
        current_user.phone_number = profile_data.phone_number
    
    db.commit()
    db.refresh(current_user)
    
    return UserProfileResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        phone_number=current_user.phone_number,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        account_status=current_user.account_status,
        roles=current_user.get_roles(),
        created_at=current_user.created_at,
        last_login=current_user.last_login
    )

@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service),
    db: Session = Depends(get_db)
):
    """
    Change user's password.
    
    Allows authenticated users to change their password.
    """
    # Verify current password
    if not auth_service.verify_password(password_data.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    current_user.password_hash = auth_service.hash_password(password_data.new_password)
    db.commit()
    
    return {"message": "Password changed successfully"}

# Admin endpoints for user management
@router.get("/users", response_model=List[UserProfileResponse])
async def get_all_users(
    current_user: User = Depends(require_permission("manage_users")),
    db: Session = Depends(get_db)
):
    """
    Get all users (admin only).
    
    Returns a list of all users in the system.
    Requires 'manage_users' permission.
    """
    users = db.query(User).all()
    return [
        UserProfileResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            phone_number=user.phone_number,
            is_active=user.is_active,
            is_verified=user.is_verified,
            account_status=user.account_status,
            roles=user.get_roles(),
            created_at=user.created_at,
            last_login=user.last_login
        )
        for user in users
    ]
```

---

*This document provides comprehensive FastAPI endpoints for the authentication system. The implementation includes proper error handling, security dependencies, and follows FastAPI best practices.*
