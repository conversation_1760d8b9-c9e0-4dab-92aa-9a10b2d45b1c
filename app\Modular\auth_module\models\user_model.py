from sqlmodel import SQLModel, Field, Column, Relationship
import uuid 
import sqlalchemy.dialects.postgresql as pg 
from sqlalchemy.sql import func
from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING, List

if TYPE_CHECKING: 
    from .session_model import Session


class User(SQLModel, table = True): 
    __tablename__ = "users"

    id: uuid.UUID = Field(
        sa_column= Column(pg.UUID, primary_key= True, default= uuid.uuid4)
    )
    username: str = Field(unique= True, nullable= False)
    email:str = Field(unique= True, nullable= False)
    password_hash: str 

    #info
    full_name: Optional[str] = Field(default=None, max_length=200)
    phone_number: Optional[str] = Field(default=None, max_length=10)
    role: str = Field(default="patient")

    #Active status
    is_active: bool = Field(default=True)
    is_verified: bool = Field(default=False)
    account_status: str = Field(default="active", max_length=20)

    # Security
    last_login: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )
    failed_login_attempts: int = Field(default=0)
    locked_until: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )

     # Timestamps
    created_at: datetime = Field(
        sa_column=Column(pg.TIMESTAMP(timezone=True), default=func.now())
    )
    updated_at: datetime = Field(
        sa_column=Column(pg.TIMESTAMP(timezone=True), 
                        default=func.now(), 
                        onupdate=func.now())
    )

    #relationships
    sessions: List["Session"] = Relationship(
        back_populates="user", 
        cascade_delete= True
    )

    def __repr__(self):
         return f"<User {self.username}>"
    
    @property 
    def is_locked (self)-> bool: 
        if not self.locked_until:
            return False
        return datetime.now(timezone.utc) < self.locked_until
    
    @property
    def display_name(self) -> str:
        return self.full_name or self.username