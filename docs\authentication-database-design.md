# Authentication System Database Design for SkinAid

## Overview

This document outlines the comprehensive authentication system database design for the SkinAid wound detection application. The design follows security best practices and supports role-based access control (RBAC) suitable for healthcare applications with different user types: patients, healthcare providers, and administrators.

## Database Architecture Philosophy

### Modular Monolith Approach
- **Single Database**: All authentication tables in one PostgreSQL database
- **Clear Module Boundaries**: Auth module is self-contained but can interact with other modules
- **Future-Ready**: Design allows easy extraction to microservice later

### Security Principles
- **Password Security**: Never store plain text passwords, use bcrypt hashing
- **Session Management**: Secure token-based authentication with expiration
- **Audit Trail**: Track all authentication events with timestamps
- **Account Security**: Email verification, password reset, account status management

## Table Relationships Overview

```
Users (1) ←→ (M) User_Roles (M) ←→ (1) Roles
Users (1) ←→ (M) Sessions
Users (1) ←→ (M) Password_Reset_Tokens
Users (1) ←→ (M) Email_Verification_Tokens
```

## Table Definitions

### 1. Users Table
**Purpose**: Core user information and authentication credentials

**Key Features**:
- UUID primary key for security
- Email-based authentication
- Account status management
- Email verification workflow
- Audit timestamps

### 2. Roles Table
**Purpose**: Define different user roles in the system

**Key Features**:
- Predefined roles: patient, healthcare_provider, admin
- Role descriptions and permissions
- Hierarchical role structure support

### 3. User_Roles Table (Junction Table)
**Purpose**: Many-to-many relationship between users and roles

**Key Features**:
- Supports multiple roles per user
- Role assignment timestamps
- Assigned by tracking (audit)

### 4. Sessions Table
**Purpose**: Manage user authentication sessions and JWT tokens

**Key Features**:
- Token-based authentication
- Session expiration management
- Device/IP tracking for security
- Revocation capability

### 5. Password_Reset_Tokens Table
**Purpose**: Secure password reset functionality

**Key Features**:
- Time-limited tokens
- One-time use tokens
- Secure token generation

### 6. Email_Verification_Tokens Table
**Purpose**: Email verification workflow

**Key Features**:
- Account activation process
- Email change verification
- Token expiration management

## PostgreSQL-Specific Optimizations

### Data Types Used
- **UUID**: For primary keys (security through obscurity)
- **TIMESTAMPTZ**: For timezone-aware timestamps
- **JSONB**: For flexible metadata storage
- **INET**: For IP address storage
- **TEXT**: For variable-length strings (more efficient than VARCHAR in PostgreSQL)

### Indexes
- **B-tree indexes**: On frequently queried columns (email, username)
- **Partial indexes**: On active sessions only
- **Composite indexes**: For complex queries
- **GIN indexes**: On JSONB columns for metadata queries

### Constraints
- **Check constraints**: For data validation (email format, status values)
- **Unique constraints**: Prevent duplicate emails/usernames
- **Foreign key constraints**: Maintain referential integrity
- **NOT NULL constraints**: Ensure required fields

## Security Considerations

### Password Security
- **Bcrypt hashing**: Industry standard with configurable work factor
- **Salt included**: Each password has unique salt
- **Work factor**: Minimum 12 rounds (adjustable based on security requirements)

### Session Security
- **JWT tokens**: Stateless authentication
- **Token expiration**: Configurable session timeout
- **Refresh tokens**: Separate refresh mechanism
- **Device tracking**: Monitor suspicious login patterns

### Account Security
- **Email verification**: Required before account activation
- **Account locking**: After failed login attempts
- **Password reset**: Secure token-based reset process
- **Audit logging**: Track all authentication events

## Healthcare-Specific Considerations

### User Roles
- **Patient**: Basic user, can view own wound data
- **Healthcare Provider**: Can view/manage patient data within scope
- **Admin**: Full system access, user management

### Compliance Considerations
- **Data retention**: Configurable retention policies
- **Audit trails**: Complete authentication history
- **Privacy**: Minimal data collection, secure storage
- **Access control**: Granular permissions based on roles

## Integration with FastAPI + SQLAlchemy

### Hybrid Approach Benefits
- **SQLAlchemy ORM**: For complex relationships and migrations
- **Raw SQL**: For performance-critical authentication queries
- **Flexibility**: Choose best tool for each operation

### FastAPI Integration
- **Dependency injection**: Authentication dependencies
- **Middleware**: Session validation middleware
- **Exception handling**: Standardized auth error responses
- **OpenAPI documentation**: Automatic API documentation

## Next Steps

1. **Implementation Order**:
   - Create database tables (DDL)
   - Implement SQLAlchemy models
   - Create authentication services
   - Build FastAPI endpoints
   - Add middleware and dependencies

2. **Testing Strategy**:
   - Unit tests for password hashing
   - Integration tests for authentication flow
   - Security tests for token validation
   - Performance tests for session queries

3. **Deployment Considerations**:
   - Database migrations
   - Environment-specific configurations
   - Monitoring and logging setup
   - Backup and recovery procedures

---

*This document serves as the foundation for implementing a secure, scalable authentication system for the SkinAid wound detection application.*
