# Authentication System - SQLAlchemy Models

## Model Definitions for FastAPI + SQLAlchemy

### Base Configuration and Imports

```python
"""
Authentication models for SkinAid wound detection system.
These models correspond to the PostgreSQL DDL statements and provide
ORM functionality for the FastAPI application.
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4

from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Integer, JSON, String, Text,
    CheckConstraint, Index, UniqueConstraint, text
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, INET, JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

# Base class for all models
Base = declarative_base()

# Custom UUID type that generates UUIDs automatically
def generate_uuid():
    return str(uuid4())
```

### 1. User Model

```python
class User(Base):
    """
    User model representing system users (patients, healthcare providers, admins).
    
    This model handles core user information, authentication credentials,
    and account status management with security best practices.
    """
    __tablename__ = "users"

    # Primary key with automatic UUID generation
    id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False), 
        primary_key=True, 
        default=generate_uuid,
        comment="Unique identifier for the user"
    )
    
    # Authentication fields
    username: Mapped[str] = mapped_column(
        String(50), 
        unique=True, 
        nullable=False,
        comment="Unique username for login"
    )
    email: Mapped[str] = mapped_column(
        String(255), 
        unique=True, 
        nullable=False,
        comment="User's email address (used for login and notifications)"
    )
    password_hash: Mapped[str] = mapped_column(
        Text, 
        nullable=False,
        comment="Bcrypt hashed password (never store plain text)"
    )
    
    # User profile information
    first_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="User's first name"
    )
    last_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="User's last name"
    )
    phone_number: Mapped[Optional[str]] = mapped_column(
        String(20),
        comment="User's phone number"
    )
    
    # Account status and verification
    is_active: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="Whether the user account is active"
    )
    is_verified: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="Whether the user's email has been verified"
    )
    is_superuser: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="Whether the user has superuser privileges"
    )
    account_status: Mapped[str] = mapped_column(
        String(20), 
        default="pending", 
        nullable=False,
        comment="Current account status"
    )
    
    # Security and audit fields
    last_login: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="Timestamp of last successful login"
    )
    failed_login_attempts: Mapped[int] = mapped_column(
        Integer, 
        default=0, 
        nullable=False,
        comment="Number of consecutive failed login attempts"
    )
    locked_until: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="Account locked until this timestamp"
    )
    
    # Metadata for extensibility (healthcare-specific data)
    metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSONB, 
        default=dict,
        comment="Additional user metadata (healthcare info, preferences, etc.)"
    )
    
    # Audit timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        nullable=False,
        comment="When the user account was created"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
        comment="When the user account was last updated"
    )
    
    # Relationships
    user_roles: Mapped[List["UserRole"]] = relationship(
        "UserRole", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    sessions: Mapped[List["Session"]] = relationship(
        "Session", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    password_reset_tokens: Mapped[List["PasswordResetToken"]] = relationship(
        "PasswordResetToken", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    email_verification_tokens: Mapped[List["EmailVerificationToken"]] = relationship(
        "EmailVerificationToken", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    # Table constraints
    __table_args__ = (
        CheckConstraint(
            "account_status IN ('pending', 'active', 'suspended', 'deactivated')",
            name="chk_users_account_status"
        ),
        CheckConstraint(
            "email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'",
            name="chk_users_email_format"
        ),
        CheckConstraint(
            "username ~* '^[A-Za-z0-9_]{3,50}$'",
            name="chk_users_username_format"
        ),
        Index("idx_users_email", "email"),
        Index("idx_users_username", "username"),
        Index("idx_users_active_verified", "is_active", "is_verified"),
        Index("idx_users_account_status", "account_status"),
        Index("idx_users_created_at", "created_at"),
        Index("idx_users_metadata", "metadata", postgresql_using="gin"),
    )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"
    
    @property
    def full_name(self) -> str:
        """Return the user's full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or self.username
    
    @property
    def is_locked(self) -> bool:
        """Check if the account is currently locked."""
        if not self.locked_until:
            return False
        return datetime.now(timezone.utc) < self.locked_until
    
    def get_roles(self) -> List[str]:
        """Get list of role names for this user."""
        return [ur.role.name for ur in self.user_roles if ur.is_active and ur.role.is_active]
```

### 2. Role Model

```python
class Role(Base):
    """
    Role model for role-based access control (RBAC).
    
    Defines different user roles in the system with hierarchical levels
    and JSON-based permissions for flexibility.
    """
    __tablename__ = "roles"

    # Primary key
    id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False), 
        primary_key=True, 
        default=generate_uuid
    )
    
    # Role identification
    name: Mapped[str] = mapped_column(
        String(50), 
        unique=True, 
        nullable=False,
        comment="Unique role name (e.g., 'patient', 'healthcare_provider')"
    )
    display_name: Mapped[str] = mapped_column(
        String(100), 
        nullable=False,
        comment="Human-readable role name"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="Detailed description of the role"
    )
    
    # Role hierarchy and permissions
    level: Mapped[int] = mapped_column(
        Integer, 
        default=0, 
        nullable=False,
        comment="Role hierarchy level (higher = more permissions)"
    )
    permissions: Mapped[List[str]] = mapped_column(
        JSONB, 
        default=list,
        comment="Array of permission strings"
    )
    
    # Role status
    is_active: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="Whether the role is active and can be assigned"
    )
    
    # Audit timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False
    )
    
    # Relationships
    user_roles: Mapped[List["UserRole"]] = relationship(
        "UserRole", 
        back_populates="role"
    )
    
    # Table constraints and indexes
    __table_args__ = (
        Index("idx_roles_name", "name"),
        Index("idx_roles_active", "is_active"),
        Index("idx_roles_level", "level"),
        Index("idx_roles_permissions", "permissions", postgresql_using="gin"),
    )
    
    def __repr__(self) -> str:
        return f"<Role(id={self.id}, name={self.name}, level={self.level})>"
    
    def has_permission(self, permission: str) -> bool:
        """Check if this role has a specific permission."""
        return permission in self.permissions
```

### 3. UserRole Junction Model

```python
class UserRole(Base):
    """
    Junction table for many-to-many relationship between users and roles.
    
    Supports role assignment tracking, expiration, and audit trail.
    """
    __tablename__ = "user_roles"

    # Composite primary key
    user_id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False), 
        ForeignKey("users.id", ondelete="CASCADE"), 
        primary_key=True
    )
    role_id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False), 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        primary_key=True
    )
    
    # Assignment metadata
    assigned_by: Mapped[Optional[str]] = mapped_column(
        PostgresUUID(as_uuid=False), 
        ForeignKey("users.id"),
        comment="User who assigned this role"
    )
    assigned_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        nullable=False,
        comment="When the role was assigned"
    )
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="Optional role expiration timestamp"
    )
    
    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="Whether this role assignment is active"
    )
    
    # Additional metadata for role assignment
    metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSONB, 
        default=dict,
        comment="Additional metadata for role assignment"
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="user_roles")
    role: Mapped["Role"] = relationship("Role", back_populates="user_roles")
    assigner: Mapped[Optional["User"]] = relationship(
        "User", 
        foreign_keys=[assigned_by]
    )
    
    # Table constraints and indexes
    __table_args__ = (
        Index("idx_user_roles_user_id", "user_id"),
        Index("idx_user_roles_role_id", "role_id"),
        Index("idx_user_roles_active", "is_active"),
        Index("idx_user_roles_expires_at", "expires_at"),
        Index("idx_user_roles_assigned_by", "assigned_by"),
    )
    
    def __repr__(self) -> str:
        return f"<UserRole(user_id={self.user_id}, role_id={self.role_id})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if this role assignment has expired."""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at
```

### 4. Session Model

```python
class Session(Base):
    """
    Session model for managing user authentication sessions and JWT tokens.
    
    Tracks active sessions, device information, and provides session
    management capabilities including revocation.
    """
    __tablename__ = "sessions"

    # Primary key
    id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False), 
        primary_key=True, 
        default=generate_uuid
    )
    
    # User reference
    user_id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False), 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False
    )
    
    # Session identification
    session_token: Mapped[str] = mapped_column(
        Text, 
        unique=True, 
        nullable=False,
        comment="JWT token or session identifier"
    )
    refresh_token: Mapped[Optional[str]] = mapped_column(
        Text, 
        unique=True,
        comment="Refresh token for token renewal"
    )
    
    # Session metadata
    device_info: Mapped[Dict[str, Any]] = mapped_column(
        JSONB, 
        default=dict,
        comment="Device information (browser, OS, device type)"
    )
    ip_address: Mapped[Optional[str]] = mapped_column(
        INET,
        comment="Client IP address"
    )
    user_agent: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="Browser user agent string"
    )
    
    # Session timing
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False,
        comment="When the session expires"
    )
    last_accessed: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.current_timestamp(),
        nullable=False,
        comment="Last time the session was used"
    )
    
    # Session status
    is_active: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False
    )
    revoked_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="When the session was revoked"
    )
    revoked_by: Mapped[Optional[str]] = mapped_column(
        PostgresUUID(as_uuid=False), 
        ForeignKey("users.id"),
        comment="User who revoked the session"
    )
    revoke_reason: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="Reason for session revocation"
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="sessions")
    revoker: Mapped[Optional["User"]] = relationship(
        "User", 
        foreign_keys=[revoked_by]
    )
    
    # Table constraints and indexes
    __table_args__ = (
        Index("idx_sessions_user_id", "user_id"),
        Index("idx_sessions_session_token", "session_token"),
        Index("idx_sessions_refresh_token", "refresh_token"),
        Index("idx_sessions_active", "is_active", "expires_at"),
        Index("idx_sessions_ip_address", "ip_address"),
        Index("idx_sessions_expires_at", "expires_at"),
        Index("idx_sessions_active_user", "user_id", "last_accessed", 
              postgresql_where=text("is_active = true AND expires_at > CURRENT_TIMESTAMP")),
    )
    
    def __repr__(self) -> str:
        return f"<Session(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if the session has expired."""
        return datetime.now(timezone.utc) > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if the session is valid (active and not expired)."""
        return self.is_active and not self.is_expired and not self.revoked_at
```

### 5. Password Reset Token Model

```python
class PasswordResetToken(Base):
    """
    Password reset token model for secure password recovery.

    Implements secure token-based password reset with expiration,
    one-time use, and audit trail.
    """
    __tablename__ = "password_reset_tokens"

    # Primary key
    id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False),
        primary_key=True,
        default=generate_uuid
    )

    # User reference
    user_id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False
    )

    # Token information
    token: Mapped[str] = mapped_column(
        Text,
        unique=True,
        nullable=False,
        comment="Secure random token (sent to user)"
    )
    token_hash: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Hashed version of token for database storage"
    )

    # Token timing and usage
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.current_timestamp(),
        nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        comment="Token expiration (typically 1-24 hours)"
    )
    used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="When the token was used for password reset"
    )

    # Security metadata
    ip_address: Mapped[Optional[str]] = mapped_column(
        INET,
        comment="IP address that requested the reset"
    )
    user_agent: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="User agent that requested the reset"
    )

    # Token status
    is_used: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    is_revoked: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="password_reset_tokens")

    # Table constraints and indexes
    __table_args__ = (
        CheckConstraint(
            "expires_at <= created_at + INTERVAL '24 hours'",
            name="chk_password_reset_token_expiry"
        ),
        Index("idx_password_reset_tokens_user_id", "user_id"),
        Index("idx_password_reset_tokens_token_hash", "token_hash"),
        Index("idx_password_reset_tokens_expires_at", "expires_at"),
        Index("idx_password_reset_tokens_active", "user_id", "expires_at",
              postgresql_where=text("is_used = false AND is_revoked = false AND expires_at > CURRENT_TIMESTAMP")),
    )

    def __repr__(self) -> str:
        return f"<PasswordResetToken(id={self.id}, user_id={self.user_id}, used={self.is_used})>"

    @property
    def is_expired(self) -> bool:
        """Check if the token has expired."""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if the token is valid for use."""
        return not self.is_used and not self.is_revoked and not self.is_expired
```

### 6. Email Verification Token Model

```python
class EmailVerificationToken(Base):
    """
    Email verification token model for email confirmation workflow.

    Supports account activation, email change verification, and
    account reactivation scenarios.
    """
    __tablename__ = "email_verification_tokens"

    # Primary key
    id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False),
        primary_key=True,
        default=generate_uuid
    )

    # User reference
    user_id: Mapped[str] = mapped_column(
        PostgresUUID(as_uuid=False),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False
    )

    # Email being verified (supports email change workflow)
    email: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Email address being verified"
    )

    # Token information
    token: Mapped[str] = mapped_column(
        Text,
        unique=True,
        nullable=False,
        comment="Secure random token (sent to email)"
    )
    token_hash: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Hashed version of token for database storage"
    )

    # Token timing and usage
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.current_timestamp(),
        nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        comment="Token expiration (typically 24-72 hours)"
    )
    verified_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="When the email was successfully verified"
    )

    # Token type for different verification scenarios
    token_type: Mapped[str] = mapped_column(
        String(20),
        default="registration",
        nullable=False,
        comment="Type of verification (registration, email_change, reactivation)"
    )

    # Security metadata
    ip_address: Mapped[Optional[str]] = mapped_column(
        INET,
        comment="IP address that requested verification"
    )
    user_agent: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="User agent that requested verification"
    )

    # Token status
    is_used: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    is_revoked: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="email_verification_tokens")

    # Table constraints and indexes
    __table_args__ = (
        CheckConstraint(
            "token_type IN ('registration', 'email_change', 'reactivation')",
            name="chk_email_verification_token_type"
        ),
        CheckConstraint(
            "expires_at <= created_at + INTERVAL '7 days'",
            name="chk_email_verification_token_expiry"
        ),
        Index("idx_email_verification_tokens_user_id", "user_id"),
        Index("idx_email_verification_tokens_email", "email"),
        Index("idx_email_verification_tokens_token_hash", "token_hash"),
        Index("idx_email_verification_tokens_expires_at", "expires_at"),
        Index("idx_email_verification_tokens_type", "token_type"),
        Index("idx_email_verification_tokens_active", "user_id", "email",
              postgresql_where=text("is_used = false AND is_revoked = false AND expires_at > CURRENT_TIMESTAMP")),
    )

    def __repr__(self) -> str:
        return f"<EmailVerificationToken(id={self.id}, email={self.email}, type={self.token_type})>"

    @property
    def is_expired(self) -> bool:
        """Check if the token has expired."""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if the token is valid for use."""
        return not self.is_used and not self.is_revoked and not self.is_expired
```

---

*This completes the SQLAlchemy model definitions for the authentication system.*
