from sqlmodel import SQLModel, Field, Column, Relationship
import uuid 
import sqlalchemy.dialects.postgresql as pg
from typing import Optional, TYPE_CHECKING
from datetime import datetime, timezone
from sqlalchemy.sql import func

if TYPE_CHECKING: 
    from .user_model import User

class Session(SQLModel, table= True): 
    __tablename__ ="sessions"

    # khóa chính
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key= True, default= uuid.uuid4)
    )

    #khóa ngoại
    user_id: uuid.UUID = Field(
        foreign_key= "User.id", 
        nullable= False, 
        index= True
    )

    #thông tin JWT tokens
    access_token_jti: str = Field(unique=True, index=True) #JWT id
    refresh_token_jti: Optional[str] = Field(
        default= None,
        unique= True, 
        index = True
    )

    #Thời gian session 
    created_at: datetime = Field(
        sa_column= Column(
            pg.TIMESTAMP(timezone=True), 
            default= func.now(), 
            nullable= False
        )
    )

    expires_at: datetime = Field(
        sa_column=Column(pg.TIMESTAMP(timezone=True), nullable=False)
    )

    last_accessed: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )

    # Device/Client info
    ip_address: Optional[str] = Field(
        default=None, 
        sa_column= Column(pg.INET)
    )
    user_agent: Optional[str] = Field(default=None, max_length=500)
    device_id: Optional[str] = Field(default=None, max_length=100)
    
    # Session status
    is_active: bool = Field(default=True)
    revoked_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )
    revoke_reason: Optional[str] = Field(default=None, max_length=200)

     # Relationships
    user: User = Relationship(
        back_populates="sessions"
    )

    def __repr__(self):
        return f"<Session {self.id} user={self.user_id}>"
    
    @property
    def is_expired(self) -> bool:
        return datetime.now(timezone.utc) > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        return self.is_active and not self.is_expired and not self.revoked_at
    
    def revoke(self, reason: str = "Manual revocation"):
        self.is_active = False
        self.revoked_at = datetime.now(timezone.utc) 
        self.revoke_reason = reason