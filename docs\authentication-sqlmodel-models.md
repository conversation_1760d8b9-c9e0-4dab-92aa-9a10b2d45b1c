# <PERSON><PERSON> Hình SQLModel Đơn Giản cho Hệ Thống Xác Thực SkinAid

## <PERSON><PERSON><PERSON>nh <PERSON>ản và Import

```python
"""
Mô hình SQLModel đơn giản cho hệ thống xác thực SkinAid.
Style đơn giản, d<PERSON> đọc và maintain.
"""

from sqlmodel import SQLModel, Field, Column, Relationship
from datetime import datetime, date
from typing import Optional, List, Dict, Any
import uuid
import sqlalchemy.dialects.postgresql as pg
from sqlalchemy.sql import func
```
```

## 1. User Model - <PERSON><PERSON> Hình Ng<PERSON>ời Dùng

```python
class User(SQLModel, table=True):
    """Model User cho database - thông tin người dùng và xác thực."""
    __tablename__ = "users"

    # Primary key
    id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID,
            nullable=False,
            primary_key=True,
            default=uuid.uuid4
        )
    )

    # Authentication fields
    username: str = Field(max_length=50, unique=True)
    email: str = Field(max_length=255, unique=True)
    password_hash: str  # Mật khẩu đã mã hóa bcrypt

    # User profile
    first_name: Optional[str] = Field(default=None, max_length=100)
    last_name: Optional[str] = Field(default=None, max_length=100)
    phone_number: Optional[str] = Field(default=None, max_length=20)

    # Account status
    is_active: bool = Field(default=True)
    is_verified: bool = Field(default=False)
    is_superuser: bool = Field(default=False)
    account_status: str = Field(default="pending", max_length=20)  # pending, active, suspended, deactivated

    # Security fields
    last_login: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )
    failed_login_attempts: int = Field(default=0)
    locked_until: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )

    # Metadata cho thông tin bổ sung (dữ liệu y tế, preferences)
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(pg.JSONB)
    )

    # Audit timestamps
    created_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )
    updated_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            onupdate=func.now(),
            nullable=False
        )
    )

    # Relationships
    user_roles: List["UserRole"] = Relationship(back_populates="user")
    sessions: List["Session"] = Relationship(back_populates="user")
    password_reset_tokens: List["PasswordResetToken"] = Relationship(back_populates="user")
    email_verification_tokens: List["EmailVerificationToken"] = Relationship(back_populates="user")

    def __repr__(self):
        return f"<User {self.username}>"

    @property
    def full_name(self) -> str:
        """Trả về họ tên đầy đủ."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or self.username

    @property
    def is_locked(self) -> bool:
        """Kiểm tra tài khoản có bị khóa không."""
        if not self.locked_until:
            return False
        return datetime.now() < self.locked_until

# API Models đơn giản
class UserCreate(SQLModel):
    """Model tạo user mới."""
    username: str
    email: str
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None

class UserRead(SQLModel):
    """Model đọc thông tin user."""
    id: uuid.UUID
    username: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    phone_number: Optional[str]
    is_active: bool
    is_verified: bool
    account_status: str
    created_at: datetime
    last_login: Optional[datetime]

class UserUpdate(SQLModel):
    """Model cập nhật user."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None

class UserLogin(SQLModel):
    """Model đăng nhập."""
    username_or_email: str
    password: str
```

## 2. Role Model - Mô Hình Vai Trò

```python
class Role(SQLModel, table=True):
    """Model Role cho database - vai trò và quyền hạn."""
    __tablename__ = "roles"

    id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID,
            nullable=False,
            primary_key=True,
            default=uuid.uuid4
        )
    )

    # Role info
    name: str = Field(max_length=50, unique=True)  # patient, healthcare_provider, admin
    display_name: str = Field(max_length=100)
    description: Optional[str] = None
    level: int = Field(default=0)  # Cấp độ phân quyền (0-10)

    # Permissions stored as JSON array
    permissions: List[str] = Field(
        default_factory=list,
        sa_column=Column(pg.JSONB)
    )

    is_active: bool = Field(default=True)

    # Audit timestamps
    created_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )
    updated_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            onupdate=func.now(),
            nullable=False
        )
    )

    # Relationships
    user_roles: List["UserRole"] = Relationship(back_populates="role")

    def __repr__(self):
        return f"<Role {self.name}>"

    def has_permission(self, permission: str) -> bool:
        """Kiểm tra vai trò có quyền cụ thể không."""
        return permission in self.permissions

# API Models
class RoleCreate(SQLModel):
    """Model tạo role mới."""
    name: str
    display_name: str
    description: Optional[str] = None
    level: int = 0
    permissions: List[str] = []

class RoleRead(SQLModel):
    """Model đọc thông tin role."""
    id: uuid.UUID
    name: str
    display_name: str
    description: Optional[str]
    level: int
    permissions: List[str]
    is_active: bool
    created_at: datetime
```

## 3. UserRole Model - Mô Hình Phân Quyền

```python
class UserRole(SQLModel, table=True):
    """Model UserRole cho database - bảng liên kết User và Role."""
    __tablename__ = "user_roles"

    # Composite primary key
    user_id: uuid.UUID = Field(
        primary_key=True,
        foreign_key="users.id",
        sa_column=Column(pg.UUID)
    )
    role_id: uuid.UUID = Field(
        primary_key=True,
        foreign_key="roles.id",
        sa_column=Column(pg.UUID)
    )

    # Assignment info
    assigned_by: Optional[uuid.UUID] = Field(
        default=None,
        foreign_key="users.id",
        sa_column=Column(pg.UUID)
    )
    assigned_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )

    is_active: bool = Field(default=True)

    # Metadata cho thông tin bổ sung
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(pg.JSONB)
    )

    # Relationships
    user: User = Relationship(back_populates="user_roles")
    role: Role = Relationship(back_populates="user_roles")

    def __repr__(self):
        return f"<UserRole user_id={self.user_id} role_id={self.role_id}>"

    @property
    def is_expired(self) -> bool:
        """Kiểm tra phân quyền có hết hạn không."""
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at

# API Models
class UserRoleCreate(SQLModel):
    """Model tạo phân quyền mới."""
    user_id: uuid.UUID
    role_name: str
    expires_at: Optional[datetime] = None

class UserRoleRead(SQLModel):
    """Model đọc thông tin phân quyền."""
    user_id: uuid.UUID
    role_id: uuid.UUID
    assigned_at: datetime
    expires_at: Optional[datetime]
    is_active: bool
```

## 4. Session Model - Mô Hình Phiên Đăng Nhập

```python
class Session(SQLModel, table=True):
    """Model Session cho database - quản lý phiên đăng nhập."""
    __tablename__ = "sessions"

    id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID,
            nullable=False,
            primary_key=True,
            default=uuid.uuid4
        )
    )

    # User reference
    user_id: uuid.UUID = Field(
        foreign_key="users.id",
        sa_column=Column(pg.UUID, nullable=False)
    )

    # Session tokens
    session_token: str = Field(unique=True)  # JWT token
    refresh_token: Optional[str] = Field(default=None, unique=True)

    # Device info
    device_info: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(pg.JSONB)
    )
    ip_address: Optional[str] = Field(
        default=None,
        sa_column=Column(pg.INET)
    )
    user_agent: Optional[str] = None

    # Session timing
    created_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )
    expires_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            nullable=False
        )
    )
    last_accessed: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )

    # Session status
    is_active: bool = Field(default=True)
    revoked_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )
    revoked_by: Optional[uuid.UUID] = Field(
        default=None,
        foreign_key="users.id",
        sa_column=Column(pg.UUID)
    )
    revoke_reason: Optional[str] = Field(default=None, max_length=100)

    # Relationships
    user: User = Relationship(back_populates="sessions")

    def __repr__(self):
        return f"<Session {self.id} user={self.user_id}>"

    @property
    def is_expired(self) -> bool:
        """Kiểm tra phiên có hết hạn không."""
        return datetime.now() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Kiểm tra phiên có hợp lệ không."""
        return self.is_active and not self.is_expired and not self.revoked_at

# API Models
class TokenResponse(SQLModel):
    """Model response cho authentication tokens."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
```

## 5. PasswordResetToken Model - Mô Hình Token Đặt Lại Mật Khẩu

```python
class PasswordResetToken(SQLModel, table=True):
    """Model PasswordResetToken cho database."""
    __tablename__ = "password_reset_tokens"

    id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID,
            nullable=False,
            primary_key=True,
            default=uuid.uuid4
        )
    )

    # User reference
    user_id: uuid.UUID = Field(
        foreign_key="users.id",
        sa_column=Column(pg.UUID, nullable=False)
    )

    # Token info
    token: str = Field(unique=True)  # Token gửi cho user
    token_hash: str  # Hash của token lưu trong DB

    # Token timing
    created_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )
    expires_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            nullable=False
        )
    )
    used_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )

    # Security info
    ip_address: Optional[str] = Field(
        default=None,
        sa_column=Column(pg.INET)
    )
    user_agent: Optional[str] = None

    # Status
    is_used: bool = Field(default=False)
    is_revoked: bool = Field(default=False)

    # Relationships
    user: User = Relationship(back_populates="password_reset_tokens")

    def __repr__(self):
        return f"<PasswordResetToken {self.id} user={self.user_id}>"

    @property
    def is_expired(self) -> bool:
        """Kiểm tra token có hết hạn không."""
        return datetime.now() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Kiểm tra token có hợp lệ không."""
        return not self.is_used and not self.is_revoked and not self.is_expired

# API Models
class PasswordResetRequest(SQLModel):
    """Model yêu cầu đặt lại mật khẩu."""
    email: str

class PasswordResetConfirm(SQLModel):
    """Model xác nhận đặt lại mật khẩu."""
    token: str
    new_password: str
```

## 6. EmailVerificationToken Model - Mô Hình Token Xác Thực Email

```python
class EmailVerificationToken(SQLModel, table=True):
    """Model EmailVerificationToken cho database."""
    __tablename__ = "email_verification_tokens"

    id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID,
            nullable=False,
            primary_key=True,
            default=uuid.uuid4
        )
    )

    # User reference
    user_id: uuid.UUID = Field(
        foreign_key="users.id",
        sa_column=Column(pg.UUID, nullable=False)
    )

    # Email being verified
    email: str = Field(max_length=255)

    # Token info
    token: str = Field(unique=True)  # Token gửi qua email
    token_hash: str  # Hash của token lưu trong DB

    # Token timing
    created_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            default=func.now(),
            nullable=False
        )
    )
    expires_at: datetime = Field(
        sa_column=Column(
            pg.TIMESTAMP(timezone=True),
            nullable=False
        )
    )
    verified_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(pg.TIMESTAMP(timezone=True))
    )

    # Token type: registration, email_change, reactivation
    token_type: str = Field(default="registration", max_length=20)

    # Security info
    ip_address: Optional[str] = Field(
        default=None,
        sa_column=Column(pg.INET)
    )
    user_agent: Optional[str] = None

    # Status
    is_used: bool = Field(default=False)
    is_revoked: bool = Field(default=False)

    # Relationships
    user: User = Relationship(back_populates="email_verification_tokens")

    def __repr__(self):
        return f"<EmailVerificationToken {self.id} email={self.email}>"

    @property
    def is_expired(self) -> bool:
        """Kiểm tra token có hết hạn không."""
        return datetime.now() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Kiểm tra token có hợp lệ không."""
        return not self.is_used and not self.is_revoked and not self.is_expired

# API Models
class EmailVerificationRequest(SQLModel):
    """Model yêu cầu xác thực email."""
    token: str

class EmailVerificationResponse(SQLModel):
    """Model response sau khi xác thực email."""
    message: str
    email_verified: bool
    account_activated: bool = False
```

## Ví Dụ Sử Dụng SQLModel với FastAPI

### 1. Cấu Hình Database Đơn Giản

```python
# app/core/database.py
"""
Cấu hình database đơn giản cho SQLModel với PostgreSQL.
"""

from sqlmodel import SQLModel, create_engine, Session
from typing import Generator
import os

# Database URL
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://skinaid_user:password@localhost:5432/skinaid_db"
)

# Tạo engine
engine = create_engine(DATABASE_URL, echo=True)

def create_db_and_tables():
    """Tạo tất cả bảng trong database."""
    SQLModel.metadata.create_all(engine)

def get_session() -> Generator[Session, None, None]:
    """Database session dependency cho FastAPI."""
    with Session(engine) as session:
        yield session
```

### 2. Service Layer Đơn Giản

```python
# app/services/auth_service.py
"""
Service layer đơn giản cho authentication.
"""

from datetime import datetime, timedelta
from typing import Optional
from sqlmodel import Session, select, or_
import bcrypt

from ..models.auth_models import User, Role, UserRole

class AuthService:
    """Service xử lý authentication đơn giản."""

    def __init__(self, session: Session):
        self.session = session

    @staticmethod
    def hash_password(password: str) -> str:
        """Mã hóa mật khẩu bằng bcrypt."""
        salt = bcrypt.gensalt(rounds=12)
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """Xác thực mật khẩu."""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except Exception:
            return False

    def create_user(self, user_data: UserCreate) -> User:
        """Tạo người dùng mới."""
        # Kiểm tra user đã tồn tại
        existing_user = self.session.exec(
            select(User).where(
                or_(User.username == user_data.username, User.email == user_data.email)
            )
        ).first()

        if existing_user:
            raise ValueError("Username hoặc email đã tồn tại")

        # Tạo user mới
        user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=self.hash_password(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            phone_number=user_data.phone_number,
            account_status="pending"
        )

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)
        return user

    def authenticate_user(self, username_or_email: str, password: str) -> Optional[User]:
        """Xác thực người dùng."""
        # Tìm user
        user = self.session.exec(
            select(User).where(
                or_(User.username == username_or_email, User.email == username_or_email)
            )
        ).first()

        if not user or user.is_locked:
            return None

        # Xác thực mật khẩu
        if not self.verify_password(password, user.password_hash):
            user.failed_login_attempts += 1
            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.now() + timedelta(minutes=30)
            self.session.commit()
            return None

        # Đăng nhập thành công
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.now()
        self.session.commit()
        return user

    def get_user_by_id(self, user_id: uuid.UUID) -> Optional[User]:
        """Lấy user theo ID."""
        return self.session.get(User, user_id)
```

### 3. FastAPI Endpoints Đơn Giản

```python
# app/api/v1/auth.py
"""
Authentication endpoints đơn giản với SQLModel.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select

from ...core.database import get_session
from ...services.auth_service import AuthService
from ...models.auth_models import User, UserCreate, UserRead, UserLogin, TokenResponse

router = APIRouter(prefix="/auth", tags=["authentication"])

def get_auth_service(session: Session = Depends(get_session)) -> AuthService:
    """Dependency để lấy AuthService."""
    return AuthService(session)

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Đăng ký người dùng mới."""
    try:
        user = auth_service.create_user(user_data)
        return {
            "message": "Đăng ký thành công",
            "user_id": str(user.id)
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=TokenResponse)
async def login_user(
    login_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Đăng nhập và trả về tokens."""
    user = auth_service.authenticate_user(
        login_data.username_or_email,
        login_data.password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Thông tin đăng nhập không đúng"
        )

    if user.account_status != "active":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Tài khoản {user.account_status}"
        )

    # Tạo tokens (cần implement SessionService)
    return TokenResponse(
        access_token="example_jwt_token",
        refresh_token="example_refresh_token",
        expires_in=1800
    )

@router.get("/users", response_model=list[UserRead])
async def get_all_users(session: Session = Depends(get_session)):
    """Lấy danh sách tất cả người dùng."""
    users = session.exec(select(User)).all()
    return [
        UserRead(
            id=user.id,
            username=user.username,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            phone_number=user.phone_number,
            is_active=user.is_active,
            is_verified=user.is_verified,
            account_status=user.account_status,
            created_at=user.created_at,
            last_login=user.last_login
        )
        for user in users
    ]
```

## Ưu Điểm SQLModel Style Đơn Giản

### 1. **Dễ Đọc và Maintain**
- Code ngắn gọn, dễ hiểu
- Ít boilerplate code
- Focus vào business logic

### 2. **Performance**
- Sử dụng func.now() thay vì datetime.now()
- Tránh timezone issues
- PostgreSQL native functions

### 3. **Type Safety**
- Full type hints với uuid.UUID
- IDE support tốt
- Catch errors sớm

## Ví Dụ Sử Dụng Đơn Giản

```python
# Tạo user mới
user = User(
    username="john_doe",
    email="<EMAIL>",
    password_hash=hash_password("password123"),
    first_name="John",
    last_name="Doe"
)

session.add(user)
session.commit()

# Query users
users = session.exec(select(User).where(User.is_active == True)).all()

# Update user
user.last_login = datetime.now()
session.commit()
```

## Kết Luận

Style SQLModel đơn giản này:
- **Dễ học**: Syntax gần với SQLAlchemy thuần
- **Hiệu quả**: Ít overhead, performance tốt
- **Practical**: Phù hợp cho dự án thực tế
- **Maintainable**: Dễ maintain và extend

Phù hợp hoàn hảo cho dự án SkinAid với yêu cầu đơn giản, hiệu quả và dễ hiểu.

---

*Tài liệu SQLModel đơn giản cho hệ thống xác thực SkinAid - Style gọn gàng, hiệu quả.*
